{"name": "testai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.8", "@mui/material": "^6.4.8", "@mui/x-charts": "^7.28.0", "@rudderstack/analytics-js": "^3.15.0", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.6.0", "@svgr/webpack": "^8.1.0", "@tanstack/react-query": "^5.66.7", "antd": "^5.23.0", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "boxicons": "^2.1.4", "classnames": "^2.5.1", "csv-parse": "^5.6.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^12.6.2", "immer": "^10.1.1", "jose": "^5.9.6", "jwt-decode": "^4.0.0", "next": "14.2.22", "nextstepjs": "1.3.0", "react": "^18", "react-dom": "^18", "react-h5-audio-player": "^3.10.0-rc.1", "react-slick": "^0.30.3", "react-use": "^17.6.0", "recharts": "^2.15.3", "slick-carousel": "^1.8.1", "stylelint-scss": "^6.10.0", "universal-cookie": "^7.2.2", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-slick": "^0.23.13", "eslint": "^8", "eslint-config-next": "14.2.22", "eslint-config-prettier": "^9.1.0", "husky": "^9.1.7", "prettier": "3.4.2", "sass": "^1.83.1", "stylelint": "^16.12.0", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-scss": "^14.0.0", "typescript": "^5"}}