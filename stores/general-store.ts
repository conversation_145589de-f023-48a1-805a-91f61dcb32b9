// src/providers/general-store-provider.ts
import { IScenario } from "@/types/scenario";
import { IRun } from "@/types/runs";
import { IAgent } from "@/types/agent";
import { IMetric } from "@/types/metric";
import { IUser } from "@/types/user";
import { IPersonality } from "@/types/personality";
import { create } from "zustand";
import { persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { devtools } from "zustand/middleware";

export type GeneralState = {
  scenarios: IScenario[];
  runs: IRun[];
  agents: IAgent[];
  currentAgentId: string;
  metrics: IMetric[];
  users: IUser[];
  personalities: IPersonality[];
  hydrated: boolean;
};

export type GeneralActions = {
  // Scenarios
  setScenarios: (scenarios: IScenario[]) => void;
  addScenarios: (scenarios: IScenario[]) => void;
  addScenario: (scenario: IScenario) => void;
  editScenario: (scenario: IScenario) => void;
  
  // Runs / results
  setRuns: (runs: IRun[]) => void;
  
  // Agents
  setAgents: (agents: IAgent[]) => void;
  editAgent: (agent: IAgent) => void;
  addAgent: (agent: IAgent) => void;
  setCurrentAgentId: (id: string) => void;
  getCurrentAgent: () => IAgent | null;
  
  // Metrics
  setMetrics: (metrics: IMetric[]) => void;
  editMetric: (metric: IMetric) => void;
  addMetric: (metric: IMetric) => void;
  deleteMetric: (id: string) => void;
  
  // Users
  setUsers: (users: IUser[]) => void;
  addUser: (user: IUser) => void;
  editUser: (user: IUser) => void;
  
  // Reset entire store state
  resetGeneralStore: () => void;
  resetExceptAgents: () => void;
  setHydrated: () => void;
};

export type GeneralStore = GeneralState & GeneralActions;

// You can use either initGeneralStore or defaultInitState.
// Here we use defaultInitState as the base initial state.
export const defaultInitState: GeneralState = {
  scenarios: [],
  runs: [],
  agents: [],
  currentAgentId: "",
  metrics: [],
  users: [],
  personalities: [],
  hydrated: false,
};

export const createGeneralStore = (initState = defaultInitState) =>
  create<GeneralStore>()(
    devtools(
      persist(
        immer((set, get) => ({
          ...initState,
          // Scenarios
          setScenarios: (scenarios: IScenario[]) => set(() => ({ scenarios })),
          addScenarios: (scenarios: IScenario[]) =>
            set((state) => ({ scenarios: [...scenarios, ...state.scenarios] })),
          addScenario: (scenario: IScenario) =>
            set((state) => ({ scenarios: [...state.scenarios, scenario] })),
          editScenario: (scenario: IScenario) =>
            set((state) => ({
              scenarios: state.scenarios.map((s) => (s.id === scenario.id ? scenario : s)),
            })),
          // Runs
          setRuns: (runs: IRun[]) => set(() => ({ runs })),
          // Agents
          setAgents: (agents: IAgent[]) => set(() => ({ agents })),
          editAgent: (agent: IAgent) =>
            set((state) => ({
              agents: state.agents.map((a) => (a.id === agent.id ? agent : a)),
            })),
          addAgent: (agent: IAgent) =>
            set((state) => ({ agents: [agent, ...state.agents] })),
          setCurrentAgentId: (id: string) => {
            const state = get();
            if (id && state.agents.some(agent => agent.id === id)) {
              set({ currentAgentId: id });
            }
          },
          getCurrentAgent: () => {
            const state = get();
            return state.agents.find((agent) => agent.id === state.currentAgentId) || null;
          },
          // Metrics
          setMetrics: (metrics: IMetric[]) => set(() => ({ metrics })),
          editMetric: (metric: IMetric) =>
            set((state) => ({
              metrics: state.metrics.map((m) => (m.id === metric.id ? metric : m)),
            })),
          addMetric: (metric: IMetric) =>
            set((state) => ({ metrics: [...state.metrics, metric] })),
          deleteMetric: (id: string) =>
            set((state) => ({ metrics: state.metrics.filter((m) => m.id !== id) })),
          // Users
          setUsers: (users: IUser[]) => set(() => ({ users })),
          addUser: (user: IUser) =>
            set((state) => ({ users: [...state.users, user] })),
          editUser: (user: IUser) =>
            set((state) => ({
              users: state.users.map((u) => (u.id === user.id ? user : u)),
            })),
          // Reset store to default state
          resetGeneralStore: () => set(() => ({ ...defaultInitState })),
          resetExceptAgents: () => set((state) => ({
            ...defaultInitState,
            agents: state.agents,
          })),
          setHydrated: () => set({ hydrated: true }),
        })),
        {
          name: "general-storage",
          partialize: (state: GeneralState) => ({
            agents: state.agents,
            currentAgentId: state.currentAgentId,
            scenarios: state.scenarios,
            runs: state.runs,
            metrics: state.metrics,
            users: state.users,
            personalities: state.personalities,
          }),
          onRehydrateStorage: () => (state) => {
            if (state) {
              state.setHydrated();
            }
          },
          version: 1,
        }
      )
    )
  );

// Create the store instance and export it
export const generalStore = createGeneralStore(defaultInitState);