import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export type ScenarioCreationStatus = 'idle' | 'creating' | 'success' | 'error';

export interface ScenarioCreationRequest {
  personality: string;
  metrics: number[];
  number_of_scenarios: number;
  bot_settings: string;
  instructions: string;
}

export interface ScenarioCreationState {
  status: ScenarioCreationStatus;
  error: string | null;
  progress: number; // 0-100
  request: ScenarioCreationRequest | null;
  socket: WebSocket | null;
  toastVisible: boolean;
  toastKey: string;
}

export interface ScenarioCreationActions {
  createScenarios: (
    agentId: string,
    token: string,
    request: ScenarioCreationRequest,
    baseUrl: string,
    onSuccess?: () => void,
    onError?: (error: any) => void
  ) => void;
  updateStatus: (status: ScenarioCreationStatus) => void;
  updateProgress: (progress: number) => void;
  setError: (error: string | null) => void;
  resetState: () => void;
  closeSocket: () => void;
  showToast: () => void;
  hideToast: () => void;
}

export type ScenarioCreationStore = ScenarioCreationState & ScenarioCreationActions;

const initialState: ScenarioCreationState = {
  status: 'idle',
  error: null,
  progress: 0,
  request: null,
  socket: null,
  toastVisible: false,
  toastKey: 'scenario-creation-toast',
};

export const useScenarioCreationStore = create<ScenarioCreationStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      createScenarios: (agentId, token, request, baseUrl, onSuccess, onError) => {
        // Close any existing socket
        const currentSocket = get().socket;
        if (currentSocket) {
          currentSocket.close();
        }

        set({
          status: 'creating',
          request,
          progress: 0,
          error: null,
          toastVisible: true,
        });

        try {
          const ws = new WebSocket(
            `${baseUrl.replace('https', 'wss')}/agents/${agentId}/generate_scenarios?token=${token}`
          );

          set({ socket: ws });

          ws.onopen = () => {
            ws.send(JSON.stringify(request));
          };

          ws.onmessage = (event) => {
            try {
              const message = JSON.parse(event.data);

              // Handle different message types
              if (message.progress) {
                set({ progress: message.progress });
              }

              // On any message without error, consider it a success
              // This will close the toast
              set({ status: 'success', progress: 100 });
              if (onSuccess) onSuccess();
            } catch (error) {
              console.error('Error parsing WebSocket message:', error);
              // Even if we can't parse the message, assume it's a success
              set({ status: 'success', progress: 100 });
              if (onSuccess) onSuccess();
            }
          };

          ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            set({
              status: 'error',
              error: 'Failed to generate scenarios. Please try again.'
            });
            if (onError) onError(error);
          };

          ws.onclose = () => {
            // Only update if we're still in creating state
            // This prevents overriding error states
            const currentStatus = get().status;
            if (currentStatus === 'creating') {
              set({ status: 'success', progress: 100 });
              if (onSuccess) onSuccess();
            }
          };
        } catch (error) {
          console.error('Error setting up WebSocket:', error);
          set({
            status: 'error',
            error: 'Failed to connect to the server. Please try again.'
          });
          if (onError) onError(error);
        }
      },

      updateStatus: (status) => set({ status }),

      updateProgress: (progress) => set({ progress }),

      setError: (error) => set({ error, status: error ? 'error' : get().status }),

      resetState: () => set({ ...initialState }),

      closeSocket: () => {
        const socket = get().socket;
        if (socket) {
          socket.close();
          set({ socket: null });
        }
      },

      showToast: () => set({ toastVisible: true }),

      hideToast: () => set({ toastVisible: false }),
    }),
    { name: 'scenario-creation-store' }
  )
);
