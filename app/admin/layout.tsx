"use client";

import "@/styles/globals.scss";
import { ReactNode } from "react";
import DashboardLayout from "@/layouts/DashboardLayout";
import { GeneralStoreProvider } from "@/providers/general-store-provider";
import ProtectedRoute from "@/app/ProtectedRoute";

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  return (
    <ProtectedRoute>
      <GeneralStoreProvider>
        <DashboardLayout>{children}</DashboardLayout>
      </GeneralStoreProvider>
    </ProtectedRoute>
  );
}
