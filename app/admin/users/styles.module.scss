@import '@/styles/variables.module';

.addButton {
  width: 188px;
  align-self: flex-end;

  :global {
    .ant-btn-icon {
      width: 24px;
      height: 24px;
    }
  }
}

.bx {
  font-size: 24px;
  cursor: pointer;
}

.dropdown {
  width: 224px;

  .dropdownTitle {
    color: $black;
  }

  .dropdownItem {
    padding: 8px !important;

    button {
      padding: 0;
    }
  }
}

.id {
  width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}