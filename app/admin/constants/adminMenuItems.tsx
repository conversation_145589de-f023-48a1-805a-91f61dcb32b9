import useHandleMenuNavigation from "@/hooks/useHandleMenuNavigation";
import Routes from "@/constants/routes";
import { MenuItem } from "@/types/common";

const useAdminMenuItems = (): MenuItem[] => {
  const navigateTo = useHandleMenuNavigation();

  return [
    {
      key: "adminPanel",
      label: "Admin panel",
      type: "group",
      children: [
        {
          key: "/admin/users",
          label: "Users",
          type: "item",
          onClick: () => navigateTo(Routes.users),
        },
      ],
    },
  ];
};

export default useAdminMenuItems;
