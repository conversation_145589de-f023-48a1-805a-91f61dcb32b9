"use client";

import {
  F<PERSON>,
  Spin,
} from "antd";
import styles from "./styles.module.scss";
import { useEffect, useState, useCallback } from "react";
import { useGeneralStore } from "@/providers/general-store-provider";
import { IScenario } from "@/types/scenario";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import Link from "next/link";
import Routes from "@/constants/routes";
import { EMetricType } from "@/types/metric";
import { useAuthStore } from "@/stores/auth-store";
import { IScenarioDto } from "@/app/api/types/scenarioDto";
import { IMetricDto } from "@/app/api/types/metricDto";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";
import ScenarioDetailsDialog from "@/components/ScenarioCard/ScenarioDetailsDialog";
import { CreateScenarioCard, ScenarioCard } from "@/components/ScenarioCard";
import BundleScenariosPreviewDialog from '@/components/ScenarioCard/BundleScenariosPreviewDialog';
import { Typography } from '@mui/material';
import { useScenarioCreationStore } from "@/stores/scenario-creation-store";
import ScenarioCreationToast from "@/components/ScenarioCreationToast";
import GenerateScenariosModal from "./components/GenerateScenariosModal/GenerateScenariosModal";
import TemplateHeaderSection from "@/components/TemplateHeaderSection/TemplateHeaderSection";
import EmptyState from "@/components/EmptyState";
import useEvaluatorAnalytics from "./components/useEvaluatorAnalytics";

export default function ScenariosScreen() {
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const evaluatorAnalytics = useEvaluatorAnalytics();
  const [, setScenarioToEdit] = useState<IScenario | null>(null);
  const [generateScenariosOpen, setGenerateScenariosOpen] = useState(false);
  const [, setCreateAIEvaluatorOpen] = useState(false);
  const { scenarios, currentAgentId, setScenarios, setMetrics } =
    useGeneralStore((state) => state);
  const { user } = useAuthStore((state) => state);

  const notify = useNotification();
  const [, setLoading] = useState(true);

  const [socket, setSocket] = useState<WebSocket | null>(null);

  // Monitor scenario creation status for toast notifications
  const scenarioCreationToastVisible = useScenarioCreationStore(state => state.toastVisible);

  const [detailsDialogOpen] = useState(false);
  const [detailsScenario, setDetailsScenario] = useState<IScenario | null>(null);

  const [selectedScenarioIds, setSelectedScenarioIds] = useState<string[]>([]);

  const [loadingCardId, setLoadingCardId] = useState<string | null>(null);

  const [previewScenarioBundle, setPreviewScenarioBundle] = useState<IScenario | null>(null);

  useEffect(() => {
    // Check if we need to open the generate scenario dialog
    const agentIdToOpenDialog = localStorage.getItem('openGenerateScenarioDialog');
    if (agentIdToOpenDialog && currentAgentId === agentIdToOpenDialog) {
      setGenerateScenariosOpen(true);
      localStorage.removeItem('openGenerateScenarioDialog');
    }
  }, [currentAgentId]);

  const fetchScenarios = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/agents/${currentAgentId}/scenarios`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${user?.token || ""}`,
        },
      });

      if (!response.ok) {
        console.error(`HTTP error! status: ${response.status}`);
        notify.error({
          message: "Failed to get scenarios",
          description: `HTTP error! status: ${response.status}`,
        });
        generalAnalyticsEvents.trackApiRequestFailed(
          "Get Scenarios",
          String(response.status),
          `Failed to get scenarios`,
        );
        return;
      }

      const responseBody: IScenarioDto[] = await response.json();

      if (responseBody.length === 0) {
        generalAnalyticsEvents.trackEmptyStateEncountered("No scenarios found");
        notify.info({
          key: "no_scenarios",
          message: "No scenarios found",
          description: "Please create a scenario.",
          duration: 3,
        });
        setScenarios([]);
        setLoading(false);
      }

      setScenarios(
        responseBody.map((scenario: IScenarioDto) => ({
          id: String(scenario.id),
          title: scenario.title,
          prompt: scenario.prompt,
          instruction: scenario.instruction,
          personality: scenario.personality,
          metrics: scenario.metrics.map((metric) => ({
            id: String(metric.id),
            name: metric.name,
            metricType: metric.type as string,
            metricPrompt: metric.prompt,
          })),
          scenarios: (Array.isArray((scenario as unknown as {scenarios?: unknown[]}).scenarios)
            ? ((scenario as unknown as {scenarios: {id: string, title: string, prompt: string}[]}).scenarios.map((s) => ({
                id: String(s.id),
                title: s.title,
                prompt: s.prompt,
              }))
            ) : []),
        })),
      );
    } catch (error) {
      console.error("Error fetching scenarios:", error);
      notify.error({
        message: "Unexpected Error",
        description: "Failed to load scenarios.",
      });
      generalAnalyticsEvents.trackApiRequestFailed(
        "Get Scenarios",
        "500",
        `Failed to load scenarios`,
      );
    } finally {
      setLoading(false);
    }
  }, [currentAgentId, user?.token, notify, generalAnalyticsEvents, setScenarios]);

  // Handle toast close and refresh scenarios if creation was successful
  const handleToastClose = useCallback(() => {
    const status = useScenarioCreationStore.getState().status;
    useScenarioCreationStore.getState().hideToast();

    // If scenarios were created successfully, refresh the list
    if (status === 'success') {
      fetchScenarios();
    }
  }, [fetchScenarios]);

  useEffect(() => {
    if (currentAgentId) {
      fetchScenarios();
    } else {
      setLoading(false);
    }
  }, [currentAgentId, fetchScenarios]);

  // Clean up WebSocket connections when component unmounts
  useEffect(() => {
    return () => {
      // Close page-level socket if it exists
      if (socket) {
        socket.close();
        setSocket(null);
      }

      // Close scenario creation socket if it exists
      useScenarioCreationStore.getState().closeSocket();
    };
  }, [socket]);

  if (!user) {
    return null;
  }

  const evaluateHandler = async (ids: number | number[]) => {
    debugger
    // For analytics, convert ids to string(s) as needed
    const analyticsIds = Array.isArray(ids) ? ids.map(String) : String(ids);
    evaluatorAnalytics.trackSimulationRunStarted(analyticsIds);

    const isBulk = Array.isArray(ids);
    const numericIds = isBulk ? (ids as number[]) : [ids as number];

    if (!isBulk) {
      evaluatorAnalytics.trackSimulationInitiated(
        currentAgentId,
        scenarios.find((scenario) => Number(scenario.id) === ids)?.metrics.length || 0,
      );
    }

    try {
      const request_body = {
        scenario_id: numericIds,
      };

      evaluatorAnalytics.trackSimulationStarted(
        currentAgentId,
        analyticsIds,
        scenarios
          .filter((scenario) => numericIds.includes(Number(scenario.id)))
          .flatMap((scenario) => scenario.metrics)
          .map((metric) => metric.name),
      );

      const ws = new WebSocket(
        `${process.env.NEXT_PUBLIC_BASE_URL_EVALUATION?.replace(
          "https",
          "wss",
        )}/evaluate?token=` + user?.token,
      );

      ws.onopen = () => {
        ws.send(JSON.stringify(request_body));
      };

      ws.onmessage = (event) => {
        const message = JSON.parse(event.data);

        if (message.status === "in_progress") {
          if (message.error_type !== "" && message.call_status === "failed") {
            evaluatorAnalytics.trackSimulationExecutionFailed({
              scenario_id: message.scenario_id,
              error_type: message.error_type,
              message: message.message,
            });
          }

          if (message.call_status === "success") {
            evaluatorAnalytics.trackSimulationExecutionSuccess({
              scenario_id: message.scenario_id,
            });
          }

          notify.info({
            type: "info",
            message: (
              <Flex vertical gap={8}>
                <span className={styles.ellipsisAnimation}>
                  Processing scenarios
                </span>
                <span>
                  {message.completed}/{message.total} completed,{" "}
                  {message.failed} failed
                </span>
              </Flex>
            ),
            showProgress: true,
            duration: 0,
            placement: "bottomRight",
            key: "in_progress",
          });
        }

        if (message.status === "calling") {
          notify.info({
            type: "info",
            message: (
              <span className={styles.ellipsisAnimation}>Call in progress</span>
            ),
            showProgress: true,
            duration: 0,
            placement: "bottomRight",
            key: "calling",
          });
        } else if (message.status === "transcribing") {
          notify.destroy("calling");
          notify.info({
            type: "info",
            message: (
              <span className={styles.ellipsisAnimation}>Transcribing</span>
            ),
            showProgress: true,
            duration: 0,
            placement: "bottomRight",
            key: "transcribing",
          });
        }

        if (message.status === "completed") {
          notify.destroy("in_progress");
          notify.destroy("calling");
          notify.destroy("transcribing");
          notify.success({
            type: "success",
            message: (
              <Flex vertical gap={8}>
                <span>Evaluation completed! 🎉</span>
                <span>
                  {message.completed}/{message.total} completed,{" "}
                  {message.failed} failed
                </span>
                <Link className={styles.link} href={Routes.results}>
                  View results
                </Link>
              </Flex>
            ),
            duration: 5,
            placement: "bottomRight",
          });
          evaluatorAnalytics.trackSimulationCompleted(
            analyticsIds,
            message.duration || "Not defined",
            scenarios
              .filter((scenario) =>
                numericIds.includes(Number(scenario.id)),
              )
              .flatMap((scenario) => scenario.metrics).length,
            message.pass_rate || "Not defined",
          );
          setLoadingCardId(null);
        }

        if (message.status === "evaluating") {
          notify.info({
            type: "info",
            message: (
              <span className={styles.ellipsisAnimation}>Evaluating</span>
            ),
            showProgress: true,
            duration: 3,
            placement: "bottomRight",
          });
        }

        if (message.status === "updated") {
          notify.info({
            type: "info",
            message: "Results updated",
            showProgress: true,
            duration: 3,
            placement: "bottomRight",
          });
        }
      };

      ws.onerror = (error) => {
        notify.destroy("calling");
        notify.destroy("in_progress");
        notify.destroy("transcribing");
        notify.destroy("success");
        console.error("WebSocket error:", error);
        setLoadingCardId(null);
      };

      ws.onclose = () => {
        notify.destroy("calling");
        notify.destroy("in_progress");
        notify.destroy("transcribing");
        setLoadingCardId(null);
      };
    } catch (error) {
      console.error(error);
      notify.destroy("calling");
      notify.destroy("in_progress");
      notify.destroy("transcribing");
      notify.destroy("success");
      setLoadingCardId(null);
    }
  };

  const handleGetMetrics = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/agents/${currentAgentId}/metrics`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${user?.token || ""}`,
        },
      });

      if (!response.ok) {
        console.error(`HTTP error! status: ${response.status}`);
        notify.error({
          message: "Failed to get metrics",
          description: `HTTP error! status: ${response.status}`,
        });
        generalAnalyticsEvents.trackApiRequestFailed(
          "Get Metrics",
          String(response.status),
          `Failed to get metrics`,
        );
        return;
      }

      const responseBody: IMetricDto[] = await response.json();
      setMetrics(
        responseBody.map((metric: IMetricDto) => ({
          id: String(metric.id),
          metricType: metric.type as EMetricType,
          metricPrompt: metric.prompt,
          name: metric.name,
        })),
      );
      return responseBody;
    } catch (error) {
      console.error("Error fetching metrics:", error);
      notify.error({
        message: "Unexpected Error",
        description: "Failed to load metrics.",
      });
      generalAnalyticsEvents.trackApiRequestFailed(
        "Get Metrics",
        "500",
        `Failed to load metrics`,
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCreateEditAIEvaluator = async (scenario?: IScenario) => {
    setScenarioToEdit(scenario || null);
    setCreateAIEvaluatorOpen(true);
  };

  const handleGenerateScenarios = async () => {
    await handleGetMetrics();
    setGenerateScenariosOpen(true);
  };

  if (user.call_fact === undefined || user.call_limit === undefined) {
    return <Spin size="large" />;
  }

  const handleCloseGenerateScenarios = () => {
    setGenerateScenariosOpen(false);
    // Scenarios will be fetched when the toast is closed if creation was successful
  };

  // const handleCloseCreateAIEvaluator = () => {
  //   setCreateAIEvaluatorOpen(false);
  //   setScenarioToEdit(null);
  // };

  return (
    <>
      {/* Add toast notification for scenario creation */}
      {scenarioCreationToastVisible && <ScenarioCreationToast onClose={handleToastClose} />}

      <Typography variant="h3" sx={{ fontFamily: 'Plus Jakarta Sans', fontSize: 34, fontWeight: 800, mb: 3, mt: 4 }}>
        Your Scenarios
      </Typography>
      <TemplateHeaderSection
        icon="/metrics-icon-template.svg"
        title="Scenarios Template"
      />
      {scenarios.length === 0 ? (
        <EmptyState
            title="No Scenarios Found"
            buttonText="Add a new scenario"
            iconSrc="/book-open-02.svg"
            iconAlt="Empty Scenarios"
            subtitle="Start and create a new one."
            onButtonClick={() => setGenerateScenariosOpen(true)} />

      ) : (
        <div className={styles.grid}>
          <CreateScenarioCard onClick={handleGenerateScenarios} />
          {scenarios.map((scenario) => (
            <ScenarioCard
              key={scenario.id}
              tag={scenario.personality}
              title={scenario.title}
              description={scenario.instruction}
              metricsCount={scenario.metrics.length}
              checked={selectedScenarioIds.includes(String(scenario.id))}
              onCheck={checked => {
                setSelectedScenarioIds(ids => checked
                  ? [...ids, String(scenario.id)]
                  : ids.filter(id => id !== String(scenario.id))
                );
              } }
              onMetricsClick={() => { } }
              onPlayClick={() => {
                const scenarioIds = scenario.scenarios && scenario.scenarios.length > 0
                  ? scenario.scenarios.map(s => Number(s.id))
                  : Number(scenario.id);
                setLoadingCardId(scenario.id);
                evaluateHandler(scenarioIds);
              } }
              onActionClick={() => handleCreateEditAIEvaluator(scenario)}
              loading={loadingCardId === scenario.id}
              onPreview={() => setPreviewScenarioBundle(scenario)}
              bundleId={scenario.id}
              onEditSuccess={fetchScenarios}
            />
          ))}
        </div>
      )}
      {generateScenariosOpen && (
        <GenerateScenariosModal
          isModalOpen={generateScenariosOpen}
          onCancel={handleCloseGenerateScenarios}
          onCustomScenario={() => {
            setGenerateScenariosOpen(false);
            setCreateAIEvaluatorOpen(true);
          }}
        />
      )}
      {/* {createAIEvaluatorOpen && (
        <CreateAIEvaluatorModal
          isModalOpen={createAIEvaluatorOpen}
          onCancel={handleCloseCreateAIEvaluator}
          initialData={scenarioToEdit}
          onSuccess={() => {
            fetchScenarios();
            router.push(Routes.simulationEvaluator);
          }}
        />
      )} */}
      <ScenarioDetailsDialog
        open={detailsDialogOpen && !!detailsScenario}
        onClose={() => setDetailsScenario(null)}
        scenario={detailsScenario ? {
          tag: detailsScenario.personality,
          title: detailsScenario.title,
          description: detailsScenario.instruction,
          metrics: detailsScenario.metrics.map(m => m.name),
          creditsRequired: (detailsScenario.metrics.length * 0.01), // Example calculation, adjust as needed
        } : {
          tag: '', title: '', description: '', metrics: [], creditsRequired: 0
        }}
        onRun={() => {
          if (detailsScenario) {
            setDetailsScenario(null);
            evaluateHandler(Number(detailsScenario.id));
          }
        }}
        onEdit={() => {
          if (detailsScenario) {
            setDetailsScenario(null);
            handleCreateEditAIEvaluator(detailsScenario);
          }
        }}
      />
      {previewScenarioBundle && (
        <BundleScenariosPreviewDialog
          open={!!previewScenarioBundle}
          scenarioBundle={previewScenarioBundle}
          onClose={() => setPreviewScenarioBundle(null)}
        />
      )}
    </>
  );
}