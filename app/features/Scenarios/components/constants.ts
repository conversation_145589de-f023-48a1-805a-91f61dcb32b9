import { ThemeConfig } from "antd";
import variables from "@/styles/variables.module.scss";

export const generateScenariosModalTheme: ThemeConfig = {
  components: {
    Modal: {
      titleColor: variables.black,
      titleFontSize: 24,
      titleLineHeight: 1.417,
    },
    Button: {
      contentFontSize: 16,
      contentLineHeight: 1,
      paddingBlock: 8,
      paddingInline: 20,
    },
    InputNumber: {
      inputFontSize: 16,
      paddingBlock: 12,
      paddingInline: 12,
    },
    Select: {
      singleItemHeightLG: 48,
    },
    Input: {
      inputFontSize: 16,
      paddingBlock: 12,
      paddingInline: 12,
    },
  },
};
