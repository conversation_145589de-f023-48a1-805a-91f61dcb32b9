.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  gap: 24px;
}

.content {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 32px;
}

.sections {
  display: flex;
  flex-direction: column;
  gap: 48px;
  width: 100%;
  margin-top: 24px;
}

.agentsContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.templateContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 24px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  gap: 16px;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  gap: 16px;
  text-align: center;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.titleContainer {
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon {
  width: 40px;
  height: 40px;
}

.title {
  font-size: 24px;
  font-weight: 800;
  color: #101828;
  font-family: 'Plus Jakarta Sans';
}

.heroSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 24px;
  padding: 48px 0;
}

.heroIcon {
  background-color: #F4EBFF;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.heroTitle {
  font-family: "Plus Jakarta Sans";
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.heroSubtitle {
  font-family: "Plus Jakarta Sans";
  color: #666666;
  font-size: 18px;
}

.sectionTitle {
  font-family: "Plus Jakarta Sans";
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 24px;
}

.gridContainer {
  background-color: #ffffff;
  flex: 1;
  width: 100%;
  padding: 24px;
  border-radius: 12px;
}

.agentCard {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0px 0px 4.89px rgba(159, 159, 159, 0.25);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0px 4px 8px rgba(159, 159, 159, 0.3);
  }
}

.agentHeader {
  margin-bottom: 12px;
}

.agentContent {
  color: #666666;
}

@media (max-width: 600px) {
  .container {
    padding: 16px;
  }

  .content {
    gap: 24px;
  }

  .sections {
    gap: 32px;
  }

  .title {
    font-size: 20px;
  }
} 