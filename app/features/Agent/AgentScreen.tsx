"use client";

import { Box, Typography, Snackbar, Alert } from "@mui/material";
import useAgentLogic from "@/hooks/useAgentLogic";
import styles from "./styles.module.scss";
import { useState } from "react";
import NoAgentsCard from "@/components/HomeCards/NoAgentCard";
import { useGeneralStore } from "@/providers/general-store-provider";
import { useAgentQueries } from "./hooks/useAgentQueries";
import AgentsSection from "./components/AgentsSection";
import TemplatesSection from "./components/TemplatesSection";
import AgentModals from "./components/AgentModals";
import PageTransition from "@/app/ui/PageTransition/PageTransition";

export default function AgentScreen() {
    const {
        agents,
        templates,
        deleteAgentMutation,
        error,
        setError,
    } = useAgentQueries();

    const {
        selectedTemplate,
        handleTemplateSelect,
        handleOnCancel,
    } = useAgentLogic();

    const [showAllTemplates, setShowAllTemplates] = useState(false);
    const [showAllAgents, setShowAllAgents] = useState(false);
    const [updateAgentModal, setUpdateAgentModal] = useState<string | null>(null);
    const currentAgentId = useGeneralStore(state => state.currentAgentId);



    const handleDeleteAgent = (agentId: string) => {
        deleteAgentMutation.mutate(agentId);
    };

    return (
        <PageTransition>
            <Box className={styles.container}>
                <Box className={styles.content}>
                    {agents.length === 0 ? (
                        <Box className={styles.noAgentsContainer}>
                            <Typography variant="h3" sx={{ fontSize: '24px', fontWeight: 800, color: '#101828', fontFamily: 'Plus Jakarta Sans', mb: 4, mt: 4 }}>
                                Your Agents
                            </Typography>
                            <NoAgentsCard onCreateAgent={() => setUpdateAgentModal("new")} />
                        </Box>
                    ) : (
                        <Box className={styles.sections}>
                            <AgentsSection
                                agents={agents}
                                loading={false}
                                currentAgentId={currentAgentId}
                                onSelect={setUpdateAgentModal}
                                onDelete={handleDeleteAgent}
                                onCreate={() => setUpdateAgentModal("new")}
                            />
                        </Box>
                    )}
                    <TemplatesSection
                        templates={templates || []}
                        onTemplateSelect={(template) => {
                            setUpdateAgentModal("new");
                            handleTemplateSelect(template);
                        }}
                        onShowAllTemplates={() => setShowAllTemplates(true)}
                    />

                    <AgentModals
                        updateAgentModal={updateAgentModal}
                        setUpdateAgentModal={setUpdateAgentModal}
                        selectedTemplate={selectedTemplate}
                        agents={agents}
                        currentAgentId={currentAgentId}
                        handleOnCancel={handleOnCancel}
                        fetchAgents={() => { }}
                        showAllTemplates={showAllTemplates}
                        setShowAllTemplates={setShowAllTemplates}
                        templates={templates}
                        handleTemplateSelect={handleTemplateSelect}
                        showAllAgents={showAllAgents}
                        setShowAllAgents={setShowAllAgents}
                        handleDeleteAgent={handleDeleteAgent}
                    />
                    <Snackbar open={!!error} autoHideDuration={6000} onClose={() => setError(null)}>
                        <Alert severity="error" onClose={() => setError(null)} sx={{ width: '100%' }}>
                            {error}
                        </Alert>
                    </Snackbar>
                </Box>
            </Box>
        </PageTransition>
    );
}
