import { Box, Typography } from "@mui/material";
import AgentCardsCarousel from "@/components/AgentCardsCarousel/AgentCardsCarousel";
import { IAgent } from "@/types/agent";
import styles from "../styles.module.scss";
import { useMemo, useCallback } from "react";
import React from "react";

/**
 * AgentsSection displays a list of agent cards in a carousel, with options to select, delete, or create agents.
 *
 * @param {Object} props - Component props
 * @param {IAgent[]} props.agents - List of agent objects to display
 * @param {string} props.currentAgentId - The currently selected agent's ID
 * @param {(id: string) => void} props.onSelect - Handler for selecting an agent
 * @param {(id: string) => void} props.onDelete - Handler for deleting an agent
 * @param {() => void} props.onCreate - Handler for creating a new agent
 */

interface AgentsSectionProps {
  agents: IAgent[];
  loading: boolean;
  currentAgentId: string;
  onSelect: (id: string) => void;
  onDelete: (id: string) => void;
  onCreate: () => void;
}

export default function AgentsSection({
  agents,
  currentAgentId,
  onSelect,
  onDelete,
  onCreate,
}: AgentsSectionProps) {

  const agentCards = useMemo(() => agents.map((agent) => ({
    id: agent.id,
    name: agent.name,
    description: agent.description,
    metricsCount: 0,
    metrics: [],
    moreCount: 0,
    currentAgentId,
    isCurrentAgent: agent.id === currentAgentId,
    onSelect: () => onSelect(agent.id),
    onDelete: () => onDelete(agent.id),
  })), [agents, currentAgentId, onSelect, onDelete]);

  const handleCreateAgent = useCallback(() => {
    onCreate();
  }, [onCreate]);

  return (
    <Box className={styles.agentsContainer}>
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 3
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
          <Typography
            variant="h4"
            sx={{
              fontSize: '32px',
              fontWeight: 800,
              color: '#101828',
              fontFamily: 'Plus Jakarta Sans',
            }}
          >
            Your Agents
          </Typography>
        </Box>
      </Box>
      <AgentCardsCarousel
        items={agentCards}
        onCreateAgent={handleCreateAgent}
      />
    </Box>
  );
} 