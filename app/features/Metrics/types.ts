import { IMetric } from "@/types/metric";
import { MetricTemplate } from "@/hooks/useMetricTemplates";

export type MetricState = {
  loading: boolean;
  error: string | null;
  metrics: IMetric[];
  currentAgentId: string | null;
};

export type MetricModalState = {
  isModalOpen: boolean;
  isTemplateDialogOpen: boolean;
  selectedTemplate: MetricTemplate | null;
  editingMetric: IMetric | null;
};

export type MetricAnalyticsEvents = {
  trackMetricCreated: (metricId: string, metricName: string, metricType: string) => void;
  trackMetricEdited: (metricId: string, metricName: string, metricType: string) => void;
  trackMetricDeleted: (metricId: string) => void;
  trackApiRequestFailed: (operation: string, status: string, message: string) => void;
};

export type MetricManagementEvents = {
  trackMetricActionMenuOpened: (metricId: string, metricName: string) => void;
  trackMetricEditClicked: (metricId: string, timestamp: number) => void;
}; 