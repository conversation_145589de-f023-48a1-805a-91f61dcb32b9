import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Box,
  Typography,
  Grid,
  CircularProgress,
  Button,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import Image from 'next/image';
import { useGeneralStore } from '@/providers/general-store-provider';
import { useNotification } from '@/context/NotificationContext/NotificationContext';
import { EMetricType } from '@/types/metric';
import { useMetricTemplate } from '../../api/useMetricTemplate';
import { useBulkCreateMetricsMutation } from '../../api/useBulkCreateMetrics';
import useGeneralAnalyticsEvents from '@/utils/useGeneralAnalyticsEvents';

interface MetricsTemplateDialogProps {
  open: boolean;
  onClose: () => void;
  onMetricsAdded?: () => void;
}

const MetricsTemplateDialog = ({
  open,
  onClose,
}: MetricsTemplateDialogProps) => {
  const [selected, setSelected] = useState<string[]>([]);
  const { currentAgentId } = useGeneralStore((state) => state);
  const notify = useNotification();
  const metricTemplates = useMetricTemplate(currentAgentId);
  const { data: templates, isLoading: isLoadingMetricTemplates } = metricTemplates;
  const bulkCreateMetricsMutation = useBulkCreateMetricsMutation(currentAgentId);
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();

  useEffect(() => {
    if (open) {
      setSelected([]);
    }
  }, [open]);

  const handleCardClick = (name: string) => {
    setSelected((prev) =>
      prev.includes(name) ? prev.filter((sid) => sid !== name) : [...prev, name]
    );
  };

  const handleSelectAll = () => {
    if (selected.length === templates?.length) {
      setSelected([]);
    } else {
      setSelected(templates?.map((t) => t.name) || []);
    }
  };

  const handleAddSelected = async () => {

    const selectedTemplates = templates?.filter((t) => selected.includes(t.name)) || [];
    bulkCreateMetricsMutation.mutate(selectedTemplates.map(template => ({
      name: template.name,
      prompt: template.prompt,
      type: template.type.toLowerCase() as EMetricType
    })), {
      onSuccess: () => {
        
        notify.success({
          message: 'Metrics added successfully',
          description: `${selected.length} metrics have been added.`,
        });
        onClose();
      },
      onError: (error: any) => {
        const errorMessage = error.response?.data?.errors;
        if (Array.isArray(errorMessage)) {
          errorMessage.forEach((err: any) => {
            notify.error({
              message: 'Failed to create metric',
              description: `Field: ${err.loc?.join('.') || ''} - ${err.msg}`,
            });
          });
        } else {
          notify.error({
            message: 'Failed to create metric',
            description: error.response?.data?.detail || error.message,
          });
        }
        generalAnalyticsEvents.trackApiRequestFailed(
          "Add Bulk Metrics",
          String(error.status || '500'),
          "Failed to add metric",
        );
      },
      onSettled: () => {
          
      },
    });

  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="xl"
      fullWidth
      PaperProps={{
        sx: { borderRadius: '24px', width: 1100, backgroundColor: '#FFFFFF' },
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1.5,
          fontFamily: 'Plus Jakarta Sans',
          fontWeight: 800,
          fontSize: 24,
          pt: 2,
          pb: 0,
        }}
      >
        <AutoAwesomeIcon sx={{ color: '#7F56D9', fontSize: 28, mr: 1 }} />
        <Typography variant="h5" sx={{ fontWeight: 800, fontFamily: 'Plus Jakarta Sans', fontSize: 24 }}>
          Generated Metrics ({templates?.length})
        </Typography>
        <Box flexGrow={1} />
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ pt: 5, pb: 0, }}>
        {isLoadingMetricTemplates ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={3} columns={12} sx={{ maxHeight: 600, overflowY: 'auto', pr: 1, mt: 2 }}>
            {templates?.map((template) => {
              const isSelected = selected.includes(template.name);
              return (
                <Grid item xs={12} sm={4} md={4} key={template.name}>
                  <Box
                    onClick={() => handleCardClick(template.name)}
                    sx={{
                      p: 2.5,
                      border: isSelected ? '2px solid #7F56D9' : '1px solid #E5E7EB',
                      borderRadius: '16px',
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      background: isSelected ? '#F9F5FF' : '#fff',
                      position: 'relative',
                      minHeight: 120,
                      boxShadow: isSelected ? '0 0 0 2px #F4EBFF' : 'none',
                      '&:hover': {
                        borderColor: '#7F56D9',
                        background: '#F9F5FF',
                      },
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    {/* Checkmark */}
                    {isSelected && (
                      <CheckCircleIcon sx={{ position: 'absolute', top: 14, right: 14, color: '#7F56D9', fontSize: 22, bgcolor: '#fff', borderRadius: '50%' }} />
                    )}
                    <Typography variant="caption" sx={{ color: '#707171', fontWeight: 500, fontFamily: 'Plus Jakarta Sans', fontSize: 12 }}>
                      {template.type || 'Binary'}
                    </Typography>
                    <Typography variant="subtitle1" sx={{ fontWeight: 700, fontFamily: 'Plus Jakarta Sans', mb: '12px' }}>
                      {template.name}
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ fontFamily: 'Plus Jakarta Sans', fontWeight: 500, display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical', overflow: 'hidden' }}
                    >
                      {template.prompt}
                    </Typography>
                  </Box>
                </Grid>
              );
            })}
          </Grid>
        )}
      </DialogContent>
      <DialogActions sx={{ px: 3, pb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Button
          variant="outlined"
          onClick={onClose}
          sx={{
            color: '#595959',
            borderColor: 'transparent',
            background: '#fff',
            textTransform: 'none',
            borderRadius: '16px',
            backgroundColor: '#F6F6F6',
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 600,
            fontSize: 16,
            px: 4,
            height: 60,
            boxShadow: 'none',
            '&:hover': { background: '#F9F5FF', borderColor: '#7F56D9' },
          }}
          startIcon={
            <Image
              src="/left-arrow-icon.svg"
              alt="Back"
              width={20}
              height={20}
              style={{ marginRight: 4 }}
            />
          }
        >
          Go back
        </Button>
        <Box display="flex" alignItems="center" gap={1.5}>
          <Box
            sx={{
              background: '#101828',
              color: '#8E97AA',
              borderRadius: '999px',
              px: 3,
              py: 2,
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 600,
              fontSize: 14,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <span style={{ color: '#fff', fontWeight: 600 }}>{selected.length}</span>
            <span style={{ color: '#8E97AA', fontWeight: 600 }}>/ {templates?.length} selected</span>
          </Box>
          <Button
            variant="text"
            onClick={handleSelectAll}
            sx={{
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 600,
              color: '#101828',
              textTransform: 'none',
              borderRadius: '999px',
              px: 2,
              py: 0.5,
              minWidth: 0,
              '&:hover': { background: '#F9F5FF' },
            }}
          >
            {selected.length === templates?.length ? 'Unselect All' : 'Select All'}
          </Button>
        </Box>
        <Button
          variant="contained"
          onClick={handleAddSelected}
          disabled={selected.length === 0}
          sx={{
            borderRadius: '16px',
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 600,
            fontSize: 16,
            background: '#7F56D9',
            textTransform: 'none',
            px: 4,
            height: 60,
            boxShadow: 'none',
            '&:hover': { background: '#6941C6' },
          }}
        >
          Add Selected
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MetricsTemplateDialog; 