"use client";

import {
    But<PERSON>,
    Dropdown,
} from "antd";
import cn from "classnames";
import styles from "./styles.module.scss";
import { EMetricType, IMetric } from "@/types/metric";
import { useState, useEffect } from "react";
import { MetricTemplate } from '@/hooks/useMetricTemplates';
import { Box, Typography, Card, Grid, IconButton, CircularProgress } from '@mui/material';
import AddIcon from "@mui/icons-material/Add";
import PageTransition from "@/app/ui/PageTransition/PageTransition";
import TemplateHeaderSection from '@/components/TemplateHeaderSection/TemplateHeaderSection';
import ExploreMoreButton from '@/components/ExploreMoreButton';
import EmptyState from '@/components/EmptyState';
import { useGeneralStore } from "@/providers/general-store-provider";
import { useMetricsAnalytics } from "../../hooks/useMetricsAnalytics";
import { useDeleteMetricMutation } from "../../api/useDeleteMetric";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import { useMetricsQuery as useFetchMetricsData } from "../../api/useMetricsQuery";
import { IMetricDto } from "@/app/api/types/metricDto";
import Tooltip from '@mui/material/Tooltip';
import MetricsModal from '@/app/features/Metrics/components/MetricsModal/MetricsModal';
import useMetricManagementAnalytics from "@/app/features/Metrics/hooks/useMetricManagementAnalytics";
import MetricsTemplateDialog from '@/app/features/Metrics/components/MetricsTemplateDialog/MetricsTemplateDialog';

export default function MetricScreen() {
    const [metricToEdit, setMetricToEdit] = useState<IMetric | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false);
    const [selectedTemplate, setSelectedTemplate] = useState<MetricTemplate | null>(null);
    const { currentAgentId, metrics, setMetrics } =
        useGeneralStore((state) => state);

    const metricManagementAnalytics = useMetricManagementAnalytics();
    const { trackMetricDeleted, trackApiRequestFailed } = useMetricsAnalytics();
    const deleteMetricMutation = useDeleteMetricMutation(currentAgentId);
    const notify = useNotification();

    const fetchMetricsData = useFetchMetricsData(currentAgentId);
    const { data: metricsData, isLoading: isLoadingMetrics } = fetchMetricsData;

    // Helper: Format metric type
    const formatMetricType = (type: string): EMetricType => {
        return (type.charAt(0).toUpperCase() + type.slice(1)) as EMetricType;
    };

    // Helper: Map API metric to local metric
    const mapMetricDtoToMetric = (metric: IMetricDto): IMetric => ({
        id: String(metric.id),
        name: metric.name,
        metricPrompt: metric.prompt,
        metricType: formatMetricType(metric.type),
    });

    // Update metrics in store when data changes
    useEffect(() => {
        if (metricsData) {
            setMetrics(metricsData.map(mapMetricDtoToMetric));
        }
    }, [metricsData, setMetrics]);

    /**
     * Deletes a metric for an agent
     * @param metricId - The ID of the metric to delete
     */
    const handleMetricDeleted = async (metricId: string) => {
        await deleteMetricMutation.mutateAsync(metricId, {
            onSuccess: () => {
                trackMetricDeleted(metricId);
                setMetrics(metrics.filter(metric => metric.id !== metricId));
                notify.success({
                    message: 'Metric deleted successfully',
                    description: 'The metric has been deleted successfully',
                });
            },
            onError: (error: any) => {
                notify.error({
                    message: 'Failed to delete metric',
                    description: error.response?.data?.detail || 'Unknown error',
                });
                trackApiRequestFailed('Delete Metric', '500', 'Failed to delete metric');
            }
        });
    };

    // Handler for metrics added from template
    const handleMetricsAddedFromTemplate = () => {
        // fetchMetricsData.refetch()
        //     .then(() => {
        //         console.log("Metrics refreshed successfully");
        //     })
        //     .catch((error: unknown) => {
        //         console.error("Error refreshing metrics:", error);
        //     })
        //     .finally(() => {
        //         setIsTemplateDialogOpen(false);
        //         setSelectedTemplate(null);
        //     });
    };

    return (
        <PageTransition>
            <Box className={styles.container}>
                <Box className={styles.header}>
                    <Typography variant="h4" className={styles.title} sx={{ fontWeight: 800, fontFamily: 'Plus Jakarta Sans' }}>
                        Your Metrics
                    </Typography>
                </Box>

                <TemplateHeaderSection
                    icon="/metrics-icon-template.svg"
                    title="Metrics Template"
                    button={
                        <ExploreMoreButton onClick={() => setIsTemplateDialogOpen(true)}>
                            Explore More
                        </ExploreMoreButton>
                    }
                />

                <Box className={styles.metricsGrid}>
                    {isLoadingMetrics ? (
                        <Box sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            height: '200px',
                            width: '100%'
                        }}>
                            <CircularProgress size={40} />
                            <Typography sx={{ mt: 2 }}>Loading metrics...</Typography>
                        </Box>
                    ) : metrics.length === 0 ? (
                        <EmptyState
                            iconSrc="/task-square-empty.svg"
                            iconAlt="Empty Metrics"
                            title="No Metrics Found"
                            subtitle="Start and create a new one."
                            buttonText="Add a new metric"
                            onButtonClick={() => setIsModalOpen(true)}
                            buttonIcon={<AddIcon sx={{ color: '#fff' }} />}
                        />
                    ) : (
                        <Grid container spacing={3}>
                            {/* Create Metric Card */}
                            <Grid item xs={12} sm={6} md={4}>
                                <Card
                                    onClick={() => setIsModalOpen(true)}
                                    sx={{
                                        height: '100%',
                                        borderRadius: '24px !important',
                                        padding: '24px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        flexDirection: 'column',
                                        backgroundColor: '#6941C6 !important',
                                        backgroundImage: 'url("/Group.svg") !important',
                                        backgroundSize: '40% !important',
                                        backgroundRepeat: 'no-repeat !important',
                                        backgroundPosition: 'right !important',
                                        cursor: 'pointer',
                                        transition: 'all 0.2s ease-in-out',
                                        position: 'relative',
                                        overflow: 'hidden',
                                        '&:hover': {
                                            transform: 'translateY(-2px)',
                                            boxShadow: '0 4px 12px rgba(127, 86, 217, 0.2)',
                                        },
                                    }}
                                >
                                    <Box sx={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        height: '100%',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        position: 'relative',
                                        zIndex: 1,
                                    }}>
                                        <Box sx={{ textAlign: 'center' }}>
                                            <IconButton
                                                sx={{
                                                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                                    borderRadius: '50%',
                                                    width: 40,
                                                    height: 40,
                                                    mb: 2,
                                                    '&:hover': {
                                                        backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                                    },
                                                }}
                                            >
                                                <AddIcon sx={{ color: '#fff', fontSize: 24 }} />
                                            </IconButton>
                                            <Typography
                                                variant="h6"
                                                sx={{
                                                    fontWeight: 600,
                                                    fontSize: "20px",
                                                    lineHeight: "30px",
                                                    color: "white",
                                                    fontFamily: "Plus Jakarta Sans",
                                                    mb: 1
                                                }}
                                            >
                                                Create Metric
                                            </Typography>
                                            <Typography
                                                variant="body2"
                                                sx={{
                                                    color: "rgba(255, 255, 255, 0.8)",
                                                    fontSize: "14px",
                                                    lineHeight: "20px",
                                                    fontFamily: "Plus Jakarta Sans",
                                                    maxWidth: "280px"
                                                }}
                                            >
                                                Create a new metric to evaluate your agent&apos;s performance
                                            </Typography>
                                        </Box>
                                    </Box>
                                </Card>
                            </Grid>
                            {/* Existing Metrics */}
                            {metrics.map((metric) => (
                                <Grid item xs={12} sm={6} md={4} key={metric.id}>
                                    <Card
                                        className={styles.metricCardContainer}
                                        sx={{
                                            p: 3,
                                            borderRadius: '24px !important',
                                            border: '1px solid #E5E7EB',
                                            boxShadow: 'none',
                                            height: '100%',
                                            display: 'flex',
                                            flexDirection: 'column',
                                            '&:hover': {
                                                borderColor: '#7F56D9',
                                                backgroundColor: '#F9F5FF',
                                            },
                                        }}
                                    >
                                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                            <Box>
                                                {Array.from(metric.name).length > 25 ? (
                                                    <Tooltip title={metric.name} placement="top" slotProps={{
                                                        tooltip: {
                                                            sx: {
                                                                background: 'rgba(255,255,255,0.1)',
                                                                backdropFilter: 'blur(10px)',
                                                                WebkitBackdropFilter: 'blur(10px)',
                                                                boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                                                                fontFamily: 'Plus Jakarta Sans',
                                                                color: '#000',
                                                                minWidth: 200,
                                                                padding: '16px 20px',
                                                                fontSize: '14px',
                                                                borderRadius: '8px',
                                                            }
                                                        }
                                                    }}>
                                                        <Typography
                                                            variant="h6"
                                                            sx={{
                                                                fontFamily: 'Plus Jakarta Sans',
                                                                fontWeight: 600,
                                                                mb: 1,
                                                                overflow: "hidden",
                                                                textOverflow: "ellipsis",
                                                                whiteSpace: "nowrap",
                                                                cursor: "pointer"
                                                            }}
                                                        >
                                                            {Array.from(metric.name).slice(0, 25).join('') + '…'}
                                                        </Typography>
                                                    </Tooltip>
                                                ) : (
                                                    <Typography
                                                        variant="h6"
                                                        sx={{
                                                            fontFamily: 'Plus Jakarta Sans',
                                                            fontWeight: 600,
                                                            mb: 1,
                                                            overflow: "hidden",
                                                            textOverflow: "ellipsis",
                                                            whiteSpace: "nowrap"
                                                        }}
                                                    >
                                                        {metric.name}
                                                    </Typography>
                                                )}
                                                <Typography variant="body2" color="text.secondary" sx={{ fontFamily: 'Plus Jakarta Sans' }}>
                                                    {metric.metricType}
                                                </Typography>
                                            </Box>
                                            <Dropdown
                                                trigger={["click"]}
                                                rootClassName={styles.dropdown}
                                                onOpenChange={(open) => {
                                                    if (open) {
                                                        metricManagementAnalytics.trackMetricActionMenuOpened(
                                                            metric.id,
                                                            metric.name,
                                                        );
                                                    }
                                                }}
                                                menu={{
                                                    items: [
                                                        {
                                                            key: "edit",
                                                            label: "Edit",
                                                            icon: <i className={cn(styles.bx, "bx bx-edit")} />,
                                                            onClick: () => {
                                                                metricManagementAnalytics.trackMetricEditClicked(
                                                                    metric.id,
                                                                    0,
                                                                );
                                                                setMetricToEdit(metric);
                                                                setIsModalOpen(true);
                                                            },
                                                        },
                                                        {
                                                            key: "delete",
                                                            label: "Delete",
                                                            icon: <i className={cn(styles.bx, "bx bx-trash")} />,
                                                            onClick: () => handleMetricDeleted(metric.id),
                                                        },
                                                    ],
                                                }}
                                            >
                                                <IconButton size="small" className={styles.actionButton}>
                                                    <i className={cn(styles.bx, "bx bx-dots-horizontal-rounded")} />
                                                </IconButton>
                                            </Dropdown>
                                        </Box>
                                        <Typography
                                            variant="body2"
                                            color="text.secondary"
                                            sx={{
                                                fontFamily: 'Plus Jakarta Sans',
                                                flexGrow: 1,
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                display: '-webkit-box',
                                                WebkitLineClamp: 3,
                                                WebkitBoxOrient: 'vertical',
                                            }}
                                        >
                                            {metric.metricPrompt}
                                        </Typography>
                                    </Card>
                                </Grid>
                            ))}
                        </Grid>
                    )}
                </Box>

                {/* Add Metric FAB */}
                {metrics.length > 0 && (
                    <Box
                        sx={{
                            position: 'fixed',
                            bottom: 24,
                            right: 24,
                        }}
                    >
                        <Button
                            type="primary"
                            onClick={() => setIsModalOpen(true)}
                            style={{
                                backgroundColor: '#7F56D9',
                                borderRadius: '50%',
                                width: '56px',
                                height: '56px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                            }}
                            icon={<i className="bx bx-plus" style={{ fontSize: '24px' }} />}
                        />
                    </Box>
                )}

                {/* Modals */}
                <MetricsModal
                    onCancel={() => {
                        setIsModalOpen(false);
                        setTimeout(() => {
                            setMetricToEdit(null);
                            setSelectedTemplate(null);
                        }, 300); // Delay to match modal close animation
                    }}
                    isModalOpen={isModalOpen}
                    initialData={metricToEdit}
                    templateData={selectedTemplate}
                />

                <MetricsTemplateDialog
                    open={isTemplateDialogOpen}
                    onClose={() => {
                        setIsTemplateDialogOpen(false);
                        setSelectedTemplate(null);
                    }}
                    onMetricsAdded={handleMetricsAddedFromTemplate}
                />
            </Box>
        </PageTransition>
    );
}
