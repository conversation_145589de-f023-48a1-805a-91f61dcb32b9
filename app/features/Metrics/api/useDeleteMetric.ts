import { useQueryClient } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";
import { deleteMetric } from "../services/metricService";

/**
 * Deletes a metric for an agent
 * @param agentId - The ID of the agent to delete the metric for
 * @param metricId - The ID of the metric to delete
 */
export function useDeleteMetricMutation(agentId: string) {
    const queryClient = useQueryClient();
    return useMutation<void, Error, string>({
        mutationFn: (metricId) => deleteMetric(agentId, metricId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['metrics', agentId] });
        },
    });
} 