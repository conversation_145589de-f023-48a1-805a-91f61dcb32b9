import { IAddNewMetricDto } from "@/app/api/types/metricDto";
import { useQueryClient } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";
import { IMetricDto } from "@/app/api/types/metricDto";
import { createMetric } from "../services/metricService";

/**
 * Creates a new metric for an agent
 * @param agentId - The ID of the agent to create the metric for
 * @param data - The data for the new metric
 */
export function useCreateMetricMutation(agentId: string) {
    const queryClient = useQueryClient();
    return useMutation<IMetricDto, Error, IAddNewMetricDto>({
        mutationFn: (data) => createMetric(agentId, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['metrics', agentId] });
        },
    });
}