import { Box, Typography, Button } from "@mui/material";
import Image from "next/image";
import styles from "./PersonalitiesEmptyState.module.scss";

interface PersonalitiesEmptyStateProps {
  onCreatePersonality: () => void;
}

const PersonalitiesEmptyState = ({
  onCreatePersonality,
}: PersonalitiesEmptyStateProps) => {
  return (
    <Box className={styles.container} sx={{ mt: 4, mb: 4 }}>
      <Box className={styles.content}>
        <p className={styles.title}>Your Personality</p>
        <Box className={styles.emptyStateCard}>
          <Box className={styles.emptyStateContent}>
            <Box className={styles.iconContainer}>
              <Image
                src="images/task-square-icon.svg"
                alt="No Personalities"
                width={57}
                height={57}
              />
            </Box>
            <Box className={styles.textContainer}>
              <Typography variant="h6" className={styles.emptyStateTitle}>
                No Personalities Found
              </Typography>
              <Typography
                variant="body2"
                className={styles.emptyStateDescription}
              >
                Start and create a new one.
              </Typography>
            </Box>
            <Button
              variant="contained"
              className={styles.newPersonalityButton}
              onClick={onCreatePersonality}
              startIcon={
                <Image
                  src="/images/add-icon-white.svg"
                  alt="Add"
                  width={24}
                  height={24}
                />
              }
            >
              New Personality
            </Button>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default PersonalitiesEmptyState;
