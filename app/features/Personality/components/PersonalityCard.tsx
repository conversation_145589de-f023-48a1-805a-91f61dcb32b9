// PersonalityCard.tsx

import React, { memo, useCallback, useState } from "react";
import { Box, Typography, useTheme, alpha, IconButton, Menu, MenuItem, ListItemIcon, Tooltip } from "@mui/material";
import { motion } from "framer-motion";
import { PersonalityCardProps } from "./PersonalityCard.types";
import Image from "next/image";
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
/**
 * A visually rich, accessible, and theme-aware card for displaying a personality/voice profile.
 */
const PersonalityCard = memo(function PersonalityCard({
  title,
  description,
  badge,
  onClick,
  forceHover,
  sx,
  "data-testid": dataTestId,
  selectable = false,
  selected = false,
  onSelect,
  showActions = true,
  onEdit,
  onDelete,
}: PersonalityCardProps) {
  const theme = useTheme();
  const [hovered] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const menuOpen = Boolean(anchorEl);

  // Accessibility: handle keyboard activation
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (selectable && onSelect && (e.key === "Enter" || e.key === " ")) {
        e.preventDefault();
        onSelect();
      } else if (!selectable && onClick && (e.key === "Enter" || e.key === " ")) {
        e.preventDefault();
        onClick();
      }
    },
    [onClick, selectable, onSelect]
  );

  // Animation
  const spring = {
    type: "spring",
    stiffness: 400,
    damping: 30,
  };

  // Colors
  const isHover = forceHover || hovered;

  // SVG asset paths
  const maskSrc = isHover
    ? "/personality-card-mask-active.svg"
    : "/personality-card-mask-1.svg";
  const backfrontSrc = isHover
    ? "/personality-card-active-backfront.svg"
    : "/personality-card-mask-backfront.svg";

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  const handleEditClick = () => {
    if (onEdit) onEdit();
    handleMenuClose();
  };
  const handleDeleteClick = () => {
    if (onDelete) onDelete();
    handleMenuClose();
  };

  return (
    <Box
      sx={{
        background: "none",
        position: "relative",
        display: "flex",
        cursor: selectable ? "pointer" : (onClick ? "pointer" : "default"),
        outline: "none",
        border: selected ? "2px solid #6941C6" : isHover ? "1px solid #6941C6" : "1px solid #EDEDED",
        boxShadow: selected ? "0px 4px 16px 0px rgba(105, 65, 198, .25)" : isHover ? "0px 4px 12px 0px rgba(105, 65, 198, .2)" : "none",
        transition: "all 0.3s ease",
        borderRadius: "20px",
        backgroundColor: "#fff",  
        padding: "12px",
        height: "240px",
        ...sx,
      }}
      tabIndex={0}
      role={selectable ? "checkbox" : onClick ? "button" : "region"}
      aria-label={title}
      aria-checked={selectable ? selected : undefined}
      aria-pressed={!selectable && !!onClick}
      data-testid={dataTestId}
      onKeyDown={handleKeyDown}
      onClick={selectable && onSelect ? onSelect : onClick}
    >
      {/* Left card SVGs stacked */}
      <Box sx={{ position: "relative", width: { xs: 80, sm: 100, md: 140 }, height: { xs: 180, sm: 200, md: 216 } }}>
        {/* Backfront SVG */}
        <Box sx={{ position: "absolute", inset: 0, zIndex: 0, top: 5, left: 15 }}>
          <Image
            src={backfrontSrc}
            alt="card background"
            width={160}
            height={190}
            draggable={false}
          />
        </Box>
        {/* Mask SVG */}
        <Box sx={{ position: "absolute", inset: 0, zIndex: 1}}>
          <Image
            src={maskSrc}
            alt="card mask" 
            width={140}
            height={215}
            draggable={false}
          />
        </Box>
        {/* Centered icon */}
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 2,
            pointerEvents: "none",
          }}
        >
        </Box>
      </Box>
      {/* Right: Content */}
      <Box
        sx={{
          flex: 1,
          minWidth: 0,
          display: "flex",
          flexDirection: "column",
          px: { xs: 2, sm: 3 },
          py: { xs: 2, sm: 2.5 },
          gap: 1,
          zIndex: 2,
        }}
      >
        {badge && (
          <Box
            sx={{
              alignSelf: "flex-start",
              mb: 0.5,
              px: 1.5,
              py: 0.5,
              borderRadius: 99,
              background: alpha(theme.palette.grey[200], 0.85),
              color: theme.palette.text.primary,
              fontWeight: 500,
              fontSize: { xs: 11},
              fontFamily: 'Plus Jakarta Sans',
              display: "inline-flex",
              alignItems: "center",
              gap: 0.5,
              letterSpacing: 0.1,
              lineHeight: 1.2,
              boxShadow: "0 1px 4px 0 " + alpha(theme.palette.grey[900], 0.04),
            }}
            aria-label={badge}
          >
            <Image src={'ai-voice-logo.svg'} alt={badge} width={16} height={16} />
            {badge}
          </Box>
        )}
        {/* Title with Tooltip for long text */}
        {title && title.length > 25 ? (
          <Tooltip title={title} placement="top" slotProps={{
            tooltip: {
              sx: {
                background: 'rgba(255,255,255,0.1)',
                backdropFilter: 'blur(10px)',
                WebkitBackdropFilter: 'blur(10px)',
                boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                fontFamily: 'Plus Jakarta Sans',
                color: '#000',
                minWidth: 200,
                padding: '16px 20px',
                fontSize: '14px',
                borderRadius: '8px',
              }
            }
          }}>
            <Typography
              variant="h6"
              sx={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 600,
                mb: 1,
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                cursor: "pointer",
                maxWidth: 180,
                display: 'block',
              }}
            >
              {title.slice(0, 25) + '…'}
            </Typography>
          </Tooltip>
        ) : (
          <Typography
            variant="h6"
            sx={{
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 600,
              mb: 1,
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              maxWidth: 180,
              display: 'block',
            }}
          >
            {title}
          </Typography>
        )}
        <Typography
          sx={{
            fontSize: { xs: 14},
            lineHeight: 1.5,
            display: "-webkit-box",
            WebkitLineClamp: 2,
            color: '#707171',
            WebkitBoxOrient: "vertical",
            overflow: "hidden",
            fontFamily: 'Plus Jakarta Sans',
            textOverflow: "ellipsis",
            maxHeight: { xs: 44, sm: 48 },
          }}
        >
          {description}
        </Typography>
      </Box>
      {/* Three-dot menu on hover */}
      {showActions && (
        <>
          <motion.div
            initial={{ opacity: 0, y: 8 }}
            animate={isHover ? { opacity: 1, y: 0 } : { opacity: 0, y: 8 }}
            transition={spring}
            style={{
              position: "absolute",
              right: 20,
              bottom: 18,
              zIndex: 3,
              pointerEvents: isHover ? "auto" : "none",
            }}
            aria-hidden={!isHover}
          >
            <IconButton size="small" sx={{ color: theme.palette.text.secondary }} onClick={handleMenuClick}>
              <svg width="24" height="24" fill="none">
                <circle cx="4" cy="12" r="2" fill="currentColor" />
                <circle cx="12" cy="12" r="2" fill="currentColor" />
                <circle cx="20" cy="12" r="2" fill="currentColor" />
              </svg>
            </IconButton>
          </motion.div>
          <Menu
            anchorEl={anchorEl}
            open={menuOpen}
            onClose={handleMenuClose}
            slotProps={{
              paper: {
                style: {
                  maxHeight: 48 * 4.5,
                  width: '16ch',
                  borderRadius: '14px',
                  marginTop: 8,
                },
              },
            }}
          >
            <MenuItem onClick={handleEditClick} sx={{ fontFamily: 'Plus Jakarta Sans', fontWeight: 600 }}>
              <ListItemIcon>
                <EditIcon fontSize="small" />
              </ListItemIcon>
              Edit
            </MenuItem>
            <MenuItem onClick={handleDeleteClick} sx={{ color: 'error.main', fontFamily: 'Plus Jakarta Sans', fontWeight: 600 }}>
              <ListItemIcon>
                <DeleteIcon fontSize="small" sx={{ color: 'error.main' }} />
              </ListItemIcon>
              Delete
            </MenuItem>
          </Menu>
        </>
      )}
      {/* Selection indicator */}
      {selectable && selected && (
        <Box sx={{
          position: "absolute",
          top: 10,
          right: 10,
          zIndex: 10,
          width: 20,
          height: 20,
          borderRadius: "50%",
          background: "#6941C6",
          border: "2px solid #6941C6",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          transition: "all 0.2s",
        }}>
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="9" cy="9" r="9" fill="#6941C6"/>
            <path d="M5 9.5L8 12.5L13 7.5" stroke="#fff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </Box>
      )}
    </Box>
  );
});

export default PersonalityCard;
