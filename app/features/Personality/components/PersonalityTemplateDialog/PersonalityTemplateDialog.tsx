import { Dialog, DialogTitle, DialogContent, Typography, DialogActions, Button } from "@mui/material"
import PersonalityCardList from "../PersonalityCardList";
import { useGetPredefinedPersonalities, useAddPersonality } from "../../lib/usePersonalityQueries";
import PersonalityCard from "../PersonalityCard";
import { useState } from "react";
import { Box } from "@mui/material";
import Image from 'next/image';
import { useNotification } from '@/context/NotificationContext/NotificationContext';

interface IPersonalityTemplatesModalProps {
    isModalOpen: boolean;
    onCancel: () => void;
}

const PersonalityTemplatesModal = ({
    isModalOpen,
    onCancel,
}: IPersonalityTemplatesModalProps) => {
    const { data: predefinedPersonalitiesData } = useGetPredefinedPersonalities();
    const [hoveredIdx, setHoveredIdx] = useState<number | null>(null);
    const [selectedId, setSelectedId] = useState<string | null>(null);
    const addPersonalityMutation = useAddPersonality();
    const notify = useNotification();

    const handleAddSelected = () => {
        if (!selectedId || !predefinedPersonalitiesData?.data) return;
        const selected = predefinedPersonalitiesData.data.find((p: any) => p.id === selectedId);
        if (!selected) return;
        addPersonalityMutation.mutate(
          {
            ...selected,
            is_predefined: true,
          },
          {
            onSuccess: () => {
              notify?.success({ message: ` Peronality ${selected.title} added successfully!` });
              handleClose();
            },
            onError: (error: any) => {
              notify?.error({ message: error.response.data.detail ?? 'Failed to add personality'});
            },
          }
        );
    };

    const handleClose = () => {
        setSelectedId(null);
        setHoveredIdx(null);
        if (addPersonalityMutation.reset) addPersonalityMutation.reset();
        onCancel();
    };

    return (
        <Dialog open={isModalOpen}
            onClose={handleClose}
            maxWidth="xl"
            fullWidth
            PaperProps={{
                sx: { borderRadius: '24px', width: 1250, backgroundColor: '#FFFFFF' },
            }}
        >
            <DialogTitle
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1.5,
                    fontFamily: 'Plus Jakarta Sans',
                    fontWeight: 800,
                    fontSize: 24,
                    pt: 2,
                    pb: 0,
                }}
            >
                <Typography variant="h5" sx={{ fontWeight: 800, fontFamily: 'Plus Jakarta Sans', fontSize: 24, mt: 2, mb: 2 }}>
                    Personality Templates ({predefinedPersonalitiesData?.data.length})
                </Typography>
            </DialogTitle>
            <DialogContent sx={{ pt: 5, pb: 0, }}   >
                <Box sx={{ maxHeight: 600, overflowY: 'auto', mb: 2, mt: 2 }}>
                    <PersonalityCardList spacing={2} sx={{
                        backgroundColor: '#fff',
                        borderRadius: '24px',
                    }}>
                        {predefinedPersonalitiesData?.data.map((p: any, idx: number) => (
                            <div
                                key={p.id}
                                onMouseEnter={() => setHoveredIdx(idx)}
                                onMouseLeave={() => setHoveredIdx(null)}
                                style={{ height: "100%" }}
                            >
                                <PersonalityCard
                                    title={p.title}
                                    description={p.description}
                                    badge={p.badge}
                                    forceHover={hoveredIdx === idx}
                                    selectable={true}
                                    selected={selectedId === p.id}
                                    onSelect={() => setSelectedId(p.id)}
                                    showActions={false}
                                />
                            </div>
                        ))}
                    </PersonalityCardList>
                </Box>

            </DialogContent>
            <DialogActions sx={{ px: 3, pb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Button
                    variant="outlined"
                    onClick={handleClose}
                    sx={{
                        color: '#595959',
                        borderColor: 'transparent',
                        background: '#fff',
                        textTransform: 'none',
                        borderRadius: '16px',
                        backgroundColor: '#F6F6F6',
                        fontFamily: 'Plus Jakarta Sans',
                        fontWeight: 600,
                        fontSize: 16,
                        px: 4,
                        height: 60,
                        boxShadow: 'none',
                        '&:hover': { background: '#F9F5FF', borderColor: '#7F56D9' },
                    }}
                    startIcon={
                        <Image
                            src="/left-arrow-icon.svg"
                            alt="Back"
                            width={20}
                            height={20}
                            style={{ marginRight: 4 }}
                        />
                    }
                >
                    Go back
                </Button>

                <Button
                    variant="contained"
                    onClick={handleAddSelected}
                    disabled={!selectedId || addPersonalityMutation.status === 'pending'}
                    sx={{
                        borderRadius: '16px',
                        fontFamily: 'Plus Jakarta Sans',
                        fontWeight: 600,
                        fontSize: 16,
                        background: '#7F56D9',
                        textTransform: 'none',
                        px: 4,
                        height: 60,
                        boxShadow: 'none',
                        '&:hover': { background: '#6941C6' },
                    }}
                >
                    {addPersonalityMutation.status === 'pending' ? 'Adding...' : 'Add Selected'}
                </Button>
            </DialogActions>
        </Dialog>
    )
}

export default PersonalityTemplatesModal;