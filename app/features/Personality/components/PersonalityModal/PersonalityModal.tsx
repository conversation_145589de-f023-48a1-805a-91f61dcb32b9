import React, { useEffect, useState, useMemo } from "react";
import { Dialog, DialogTitle, DialogContent, Box, Typography, IconButton } from "@mui/material";
import Image from "next/image";
import styles from "./PersonalityModal.module.scss";
import { IPersonality } from "@/types/personality";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";
import PersonalityOptionCard from "./PersonalityOptionCard";
import CreatePersonalityStep from "./CreatePersonalityStep";
import GeneratePersonalityStep from "./GeneratePersonalityStep";
import usePersonalityAnalytics from "./../../usePersonalityAnalytics";
import CloseIcon from "@mui/icons-material/Close";
import { useAddPersonality, useUpdatePersonality, useGetLanguages } from '../../lib/usePersonalityQueries';
import { useNotification } from '@/context/NotificationContext/NotificationContext';

interface IPersonalityModalProps {
  onCancel: () => void;
  isModalOpen: boolean;
  initialData: IPersonality | null;
  editMode?: boolean;
  onLibraryClick?: () => void;
}

const PersonalityModal = ({
  onCancel,
  isModalOpen,
  initialData,
  editMode = false,
  onLibraryClick,
}: IPersonalityModalProps) => {
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const personalityAnalytics = usePersonalityAnalytics();
  const [modalOpenedTime, setModalOpenedTime] = useState<number | null>(null);

  const notify = useNotification();
  const { data: languagesData } = useGetLanguages();
  const languages = useMemo(() => languagesData?.data || [], [languagesData]);
  const addPersonalityMutation = useAddPersonality();
  const updatePersonalityMutation = useUpdatePersonality();
  
  // Multi-step flow states
  const [selectedOption, setSelectedOption] = useState<"scratch" | "library" | null>(null);
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [personalityData, setPersonalityData] = useState<{
    name: string;
    language: string;
    description: string;
    count?: number;
    instructions?: string;
  }>({
    name: initialData?.title || "",
    language: languages.find((l: any) => l.id === initialData?.language_id)?.code || "en",
    description: initialData?.description || "",
    count: 4,
    instructions: "",
  });

  const [, setNameError] = useState<string | null>(null);
  const [, setDescriptionError] = useState<string | null>(null);

  useEffect(() => {
    if (isModalOpen) {
      setModalOpenedTime(Date.now());
    }
  }, [isModalOpen]);



  useEffect(() => {
    if (editMode && initialData) {
      setCurrentStep(2);
      personalityAnalytics.trackUpdatePersonality();
      return;
    }
  }, [currentStep, selectedOption, initialData, editMode, personalityAnalytics]);

  useEffect(() => {
    if (isModalOpen && !editMode) {
      setCurrentStep(1);
      setSelectedOption(null);
      setPersonalityData({
        name: "",
        language: languages[0]?.code || "en",
        description: "",
        count: 4,
        instructions: "",
      });
    }
  }, [isModalOpen, editMode, languages]);

  const handleOptionSelect = (option: "scratch" | "library") => {
    setSelectedOption(option);
    setCurrentStep(2);
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleCreateOrUpdate = async (values: { name: string; language: string; description: string }) => {
    // Find language_id from code
    const selectedLang = languages.find((l: any) => l.code === values.language);
    const language_id = selectedLang ? selectedLang.id : undefined;
    if (!language_id) {
      notify?.error({ message: 'Invalid language selected.' });
      return;
    }
    if (editMode && initialData) {
      // Update
      updatePersonalityMutation.mutate(
        {
          personality_id: initialData.id,
          data: {
            title: values.name,
            description: values.description,
            language_id,
            is_active: initialData.is_active,
            is_predefined: false,
          },
        },
        {
          onSuccess: () => {
            notify?.success({ message: 'Personality updated successfully!' });
            onCancel();
          },
          onError: (error: any) => {
            const msg = error?.response?.data?.detail || error?.message || 'Failed to update personality.';
            notify?.error({ message: msg });
          },
        }
      );
    } else {
      // Create
      addPersonalityMutation.mutate(
        {
          title: values.name,
          description: values.description,
          language_id,
          is_active: true,
          is_predefined: false,
        },
        {
          onSuccess: () => {
            notify?.success({ message: 'Personality created successfully!' });
            onCancel();
          },
          onError: (error: any) => {
            const errors = error?.response?.data?.errors;
            if (Array.isArray(errors) && errors.length > 0) {
              errors.forEach((err: any) => {
                if (err.loc?.includes('title')) setNameError(err.msg);
                else if (err.loc?.includes('description')) setDescriptionError(err.msg);
                notify?.error({ message: err.msg });
              });
            } else {
              const msg = error?.response?.data?.detail || error?.message || 'Failed to create personality.';
              notify?.error({ message: msg });
            }
          },
        }
      );
    }
  };

  const handleGeneratePersonality = (values: { count: number; instructions: string }) => {
    setPersonalityData({
      ...personalityData,
      count: values.count,
      instructions: values.instructions,
    });
    handleCancel();
  };

  const handleCancel = () => {
    const timeSpent = modalOpenedTime ? Date.now() - modalOpenedTime : 0;
    generalAnalyticsEvents.trackModalCloseButtonClicked(
      "PersonalityModal",
      timeSpent,
      "-/-",
    );
    setCurrentStep(1);
    setSelectedOption(null);
    onCancel();
  };

  return (
    <Dialog open={isModalOpen} onClose={handleCancel} maxWidth="sm"
      fullWidth
      slotProps={{
        paper: {
          sx: { borderRadius: '24px', width: 600, backgroundColor: '#FFFFFF' },
        },
      }}>

      <DialogTitle sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        fontFamily: 'Plus Jakarta Sans',
        fontWeight: 800,
        fontSize: 24,
        pt: 3,
        pb: 2,
      }}>
        <Box className={styles.modalHeader}>
          <Box className={styles.titleContainer}>
            <Box sx={{
              width: 48,
              height: 48,
              borderRadius: '14.67px',
              background: '#fff',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0px 0px 4.89px rgba(159, 159, 159, 0.25)',
            }}>
              <Image src={currentStep === 3 ? "/images/voice-icon.svg" : "/images/personality-icon.svg"} alt={currentStep === 3 ? "Voice" : "Personality"} width={28} height={28} />
            </Box>
            <Typography        variant="h5"
              sx={{ fontWeight: 700, fontFamily: 'Plus Jakarta Sans', fontSize: 20, color: '#1C1C1C' }}>
              {initialData
                ? "Update Personality"
                : currentStep === 3
                  ? "Select Voice"
                  : selectedOption === "scratch"
                    ? "Create Personality"
                    : selectedOption === "library"
                      ? "Generate Personality"
                      : "Create New Personality"}
            </Typography>
          </Box>

          <Box flexGrow={1} />
          <IconButton onClick={handleCancel} size="small">
            <CloseIcon />
          </IconButton>

        </Box>
      </DialogTitle>
      <DialogContent sx={{ pt: 3, pb: 0 }}>
        {(currentStep === 2 && (selectedOption === "scratch" || editMode)) || initialData ? (
          <CreatePersonalityStep
            onNext={handleCreateOrUpdate}
            initialValues={editMode && initialData ? {
              name: initialData.title,
              description: initialData.description,
              language: languages.find((l: any) => l.id === (initialData as any).language_id)?.code || "en",
            } : {
              name: personalityData.name,
              language: personalityData.language,
              description: personalityData.description,
            }}
            editMode={editMode}
            languages={languages}
            loading={addPersonalityMutation.status === 'pending' || updatePersonalityMutation.status === 'pending'}
          />
        ) : (
          <>
            {currentStep === 1 && !editMode && (
              <Box className={styles.optionsContainer}>
                <PersonalityOptionCard
                  title="Create Personality from scratch"
                  description="Define your own personality with custom parameters and instructions"
                  iconSrc="/images/personality-icon.svg"
                  isSelected={selectedOption === "scratch"}
                  onClick={() => handleOptionSelect("scratch")}
                  variant="primary"
                />
                <PersonalityOptionCard
                  title="Generate Personality from Pre-Defined Template"
                  description="Choose from a pre-defined template to create a personality based on your requirements"
                  iconSrc="/images/sparkles.svg"
                  isSelected={selectedOption === "library"}
                  onClick={() => {
                    if (onLibraryClick) onLibraryClick();
                  }}
                  variant="secondary"
                />
              </Box>
            )}
            {currentStep === 2 && selectedOption === "library" && (
              <GeneratePersonalityStep
                onBack={handleBack}
                onGenerate={handleGeneratePersonality}
                initialValues={{
                  count: personalityData.count || 4,
                  instructions: personalityData.instructions || "",
                }}
              />
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default PersonalityModal;
