@import '@/styles/variables.module';

.card {
  width: 100%;
  border-radius: 20px;
  padding: 13px 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #E3E3E3;
  transition: all 0.2s ease;
  &:hover {
    border-color: #7F56D9;
    background-color: #F9F5FF;
  }

}

.content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.iconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 12px;
  padding: 8px;
}

.primaryIcon {
  background-color: rgba(127, 86, 217, 0.1);

  img {
    filter: invert(36%) sepia(44%) saturate(1103%) hue-rotate(223deg) brightness(93%) contrast(93%);
  }
}

.secondaryIcon {
  background-color: #F7F7F7;

  img {
    filter: invert(48%) sepia(11%) saturate(341%) hue-rotate(179deg) brightness(94%) contrast(87%);
  }
}

.textContainer {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.title {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
  color: #1B1B1B;
  margin: 0;
}

.description {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.71;
  color: #1B1B1B;
  margin: 0;
}

.primary {
  // Primary card styles
}

.secondary {
  // Secondary card styles
}
