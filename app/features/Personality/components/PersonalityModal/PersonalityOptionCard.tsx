import React, { useState } from 'react';
import { Box, Typography, Paper } from '@mui/material';

import PersonalityIcon from './../../../../../public/personality-icon.svg';
import SparklesIcon from './../../../../../public/images/sparkles.svg';

interface PersonalityOptionCardProps {
  title: string;
  description: string;
  iconSrc: string;
  isSelected?: boolean;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

const iconMap: Record<string, React.FC<React.SVGProps<SVGSVGElement>>> = {
  '/images/personality-icon.svg': PersonalityIcon,
  '/images/sparkles.svg': SparklesIcon,
};

const PersonalityOptionCard: React.FC<PersonalityOptionCardProps> = ({
  title,
  description,
  iconSrc,
  onClick,
}) => {
  const [, setHover] = useState(false);
  const IconComponent = iconMap[iconSrc];
  return (
    <Paper
      onClick={onClick}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
      elevation={0}
      sx={{
        border: '1.5px solid #E5E7EB',
        borderRadius: '20px',
        background: '#fff',
        p: 3,
        display: 'flex',
        flexDirection: 'column',
        gap: 1,
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        '&:hover': {
          borderColor: '#7F56D9',
          background: '#F9F5FF',
          '& .scratch-icon-container': {
            background: '#E9D7FE',
          },
          '& .scratch-icon-image': {
            filter: 'brightness(1) saturate(1)',
          },
          '& .scratch-icon-svg path': {
            stroke: '#7F56D9',
          }
        },
      }}
    >
      <Box
        sx={{
          width: 40,
          height: 40,
          borderRadius: '12px',
          background: '#F7F7F7',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          mb: 0.5,
          transition: 'all 0.2s ease',
        }}
      >
        {IconComponent ? (
          <IconComponent
            width={24}
            height={24}
          />
        ) : null}
      </Box>
      <Typography sx={{ fontWeight: 600, fontFamily: 'Plus Jakarta Sans', fontSize: 16, color: '#101828',}}>
        {title}
      </Typography>
      <Typography sx={{ fontWeight: 400, fontFamily: 'Plus Jakarta Sans', fontSize: 14, color: '#667085', mt: 0.5,}}>
        {description}
      </Typography>
    </Paper>
  );
};

export default PersonalityOptionCard;
