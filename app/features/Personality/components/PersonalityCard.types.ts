// PersonalityCard.types.ts

import { SxProps, Theme } from "@mui/material/styles";
import { ReactNode } from "react";

/**
 * Props for the PersonalityCard component.
 */
export interface PersonalityCardProps {
  /**
   * The name/title of the personality (e.g., "<PERSON> – Old Storyteller").
   */
  title: string;
  /**
   * The description or summary text.
   */
  description: string;
  /**
   * Optional: A label or badge to display (e.g., "Speaks Fast").
   */
  badge?: string;
  /**
   * Optional: Icon to display in the left card area. If not provided, a default waveform icon is shown.
   */
  icon?: ReactNode;
  /**
   * Optional: Card size variant.
   * - "medium" (default)
   * - "small"
   */
  size?: "medium" | "small";
  /**
   * Optional: Called when the card is clicked or activated via keyboard.
   */
  onClick?: () => void;
  /**
   * Optional: If true, shows the hover state (for demo/testing).
   */
  forceHover?: boolean;
  /**
   * Optional: Custom styles via MUI sx prop.
   */
  sx?: SxProps<Theme>;
  /**
   * Optional: Test id for testing.
   */
  "data-testid"?: string;
  /**
   * Optional: If true, the card can be selected (shows selection UI).
   */
  selectable?: boolean;
  /**
   * Optional: If true, the card is currently selected (for selectable mode).
   */
  selected?: boolean;
  /**
   * Optional: Called when the card is selected (for selectable mode).
   */
  onSelect?: () => void;
  /**
   * Optional: If true, shows the action button (three-dot menu) on the card. Default: true
   */
  showActions?: boolean;
  /**
   * Optional: Called when the edit action is triggered from the action menu.
   */
  onEdit?: () => void;
  /**
   * Optional: Called when the delete action is triggered from the action menu.
   */
  onDelete?: () => void;
}