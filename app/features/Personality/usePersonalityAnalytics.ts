import { useMemo } from "react";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import { useAuthStore } from "@/stores/auth-store";
import { usePathname } from "next/navigation";

const usePersonalityAnalytics = () => {
  const analytics = useRudderStackAnalytics();
  const { user } = useAuthStore((state) => state);
  const pathname = usePathname();

  return useMemo(() => {
    const track = (
      eventName: string,
      payload: Record<string, unknown> = {},
    ) => {
      if (!analytics) return;
      analytics.track(eventName, {
        user_id: user?.id,
        userId: user?.id,
        email: user?.email,
        domain: user?.email.split("@")[1],
        timestamp: new Date().toISOString(),
        app: "test.ai",
        category: "Personality",
        page_name: pathname,
        ...payload,
      });
    };

    return {
      trackPersonalityTraitAdjusted: (
        agent_id: string,
        trait_name: string,
        old_value: string,
        new_value: string,
      ) =>
        track("personality_trait_adjusted", {
          agent_id,
          trait_name,
          old_value,
          new_value,
        }),
      trackUpdatePersonality: () => {
        track("update_personality");
      },
    };
  }, [analytics, user?.id]);
};

export default usePersonalityAnalytics;
