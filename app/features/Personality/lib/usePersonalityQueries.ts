import { useMutation, useQuery, useQueryClient, UseMutationOptions, UseQueryResult } from '@tanstack/react-query';
import {
  addPersonality,
  getPersonalities,
  getPredefinedPersonalities,
  updatePersonality,
  deletePersonality,
} from '../services/personalityService';
import { AxiosResponse } from 'axios';
import { getLanguages } from '@/services/langService';

export interface Personality {
  title: string;
  description: string;
  language_id: number;
  id: number;
  is_active: boolean;
  is_predefined: boolean;
}

export type PersonalityList = Personality[];

export type CreatePersonalityBody = Omit<Personality, 'id'>;

export type UpdatePersonalityBody = Partial<Omit<Personality, 'id'>>;

/**
 * Fetch all personalities.
 */
export function useGetPersonalities(): UseQueryResult<AxiosResponse, Error> {
  return useQuery({ queryKey: ['personalities'], queryFn: getPersonalities });
}

/**
 * Fetch predefined personalities.
 */
export function useGetPredefinedPersonalities(): UseQueryResult<AxiosResponse, Error> {
  return useQuery({ queryKey: ['personalities', 'predefined'], queryFn: getPredefinedPersonalities });
}

/**
 * Add a new personality.
 */
export function useAddPersonality(options?: UseMutationOptions<AxiosResponse, Error, any, unknown>) {
  const queryClient = useQueryClient();
  return useMutation<AxiosResponse, Error, CreatePersonalityBody, unknown>({
    mutationFn: (data) => addPersonality(data),
    ...options,
    onSuccess: (data: AxiosResponse, variables: CreatePersonalityBody, context: unknown) => {
      queryClient.invalidateQueries({ queryKey: ['personalities'] });
        if (options && options.onSuccess) {
          options.onSuccess(data, variables, context);
        }
      },
    }
  );
}

/**
 * Update a personality by id.
 */
export function useUpdatePersonality(options?: UseMutationOptions<AxiosResponse, Error, { personality_id: string | number; data: any }, unknown>) {
  const queryClient = useQueryClient();
  return useMutation<AxiosResponse, Error, { personality_id: string | number; data: any }, unknown>({
    mutationFn: ({ personality_id, data }) => updatePersonality(personality_id, data),
    ...options,
    onSuccess: (data: AxiosResponse, variables: { personality_id: string | number; data: any }, context: unknown) => {
      queryClient.invalidateQueries({ queryKey: ['personalities'] });
        if (options && options.onSuccess) {
          options.onSuccess(data, variables, context);
        }
      },
    }
  );
}

/**
 * Delete a personality by id.
 */
export function useDeletePersonality(options?: UseMutationOptions<AxiosResponse, Error, string | number, unknown>) {
  const queryClient = useQueryClient();
  return useMutation<AxiosResponse, Error, string | number, unknown>({
    mutationFn: (personality_id) => deletePersonality(personality_id),
    ...options,
    onSuccess: (data: AxiosResponse, variables: string | number, context: unknown) => {
      queryClient.invalidateQueries({ queryKey: ['personalities'] });
      if (options && options.onSuccess) {
        options.onSuccess(data, variables, context);
      }
    },
  });
}

/**
 * Fetch all languages.
 */
export function useGetLanguages(): UseQueryResult<AxiosResponse, Error> {
  return useQuery({ queryKey: ['languages'], queryFn: getLanguages });
} 