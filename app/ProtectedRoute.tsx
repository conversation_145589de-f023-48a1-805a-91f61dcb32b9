"use client";

import { useRouter, usePathname } from "next/navigation";
import { useEffect } from "react";
import { Spin } from "antd";
import { useAuthStore } from "@/stores/auth-store";
import { generalStore } from "@/stores/general-store";

export default function ProtectedRoute({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isAuthenticated, hydrated, user, logout } = useAuthStore(
    (state) => state,
  );
  const { agents } = generalStore((state) => state);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (hydrated && !isAuthenticated) {
      router.push("/login");
      return;
    }



    if (!user || !user.expiresIn) {
      return;
    }

    const { expiresIn } = user;
    const now = Date.now();
    if (new Date(expiresIn).getTime() < now) {
      logout();
      router.push("/login");
      return;
    }

    if (hydrated && isAuthenticated && !user.isAdministrator  && !user.subscription && pathname !== "/pricing-list") {
      router.push("/pricing-list");
      return;
    }
  }, [isAuthenticated, hydrated, user, logout, router, pathname]);

  if(user?.isAdministrator) {
    return <>{children}</>;
  }
  
  if (!hydrated) {
    return <Spin fullscreen />;
  }

  if (!isAuthenticated) {
    return null;
  }

  // Allow access to pricing list without subscription
  if (pathname === "/pricing-list") {
    return <>{children}</>;
  }

  // Check if user has subscription for other routes
  if (!user?.subscription) {
    return null;
  }

  // Allow access to starter screen if user has subscription but no agents
  if (agents.length === 0 && pathname === "/starter" && !user?.isAdministrator) {
    return <>{children}</>;
  }

  // For all other routes, require at least one agent
  if (agents.length === 0 && !user?.isAdministrator) {
    router.push("/starter");
    return null;
  }

  return <>{children}</>;
}
