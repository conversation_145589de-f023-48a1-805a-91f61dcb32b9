"use client";

import { motion } from "framer-motion";
import React, { ReactNode } from "react";

interface PageTransitionProps {
  children: ReactNode;
}

const pageVariants = {
  initial: { opacity: 0.0 },
  animate: { opacity: 1 },
  exit: { opacity: 0.2 },
};

const pageTransition = {
  duration: 0.3,
  ease: "easeInOut",
};

export default function PageTransition({ children }: PageTransitionProps) {
  return (
    <motion.div
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={pageTransition}
      style={{ height: "100%" }}
    >
      {children}
    </motion.div>
  );
}
