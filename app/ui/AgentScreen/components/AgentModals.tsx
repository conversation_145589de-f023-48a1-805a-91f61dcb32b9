import UpdateAgentModal from "@/app/ui/AgentScreen/components/UpdateAgentModal/UpdateAgentModal";
import ExploreAllDialog from "@/components/TemplateCard/ExploreAllDialog";
import { TemplateCardData } from "@/components/TemplateCard/TemplateCard";
import { IAgent } from "@/types/agent";

interface AgentModalsProps {
  updateAgentModal: string | null;
  showAllAgents: boolean;
  selectedTemplate: TemplateCardData | null;
  agents: IAgent[];
  currentAgentId: string;
  showAllTemplates: boolean;
  templates: TemplateCardData[];
  handleOnCancel: () => void;
  fetchAgents: () => void;
  setShowAllTemplates: (open: boolean) => void;
  setUpdateAgentModal: (id: string | null) => void;
  handleTemplateSelect: (template: TemplateCardData) => void;
  setShowAllAgents: (open: boolean) => void;
  handleDeleteAgent: (agentId: string) => void;
}

/**
 * AgentModals manages and renders all modal dialogs for the agents page, including agent creation/editing and exploring templates/agents.
 *
 * @param {Object} props - Component props
 * @param {string | null} props.updateAgentModal - The ID of the agent to update, or 'new' for creation, or null if closed
 * @param {(id: string | null) => void} props.setUpdateAgentModal - Handler to set the update agent modal state
 * @param {TemplateCardData | null} props.selectedTemplate - The currently selected template for agent creation
 * @param {IAgent[]} props.agents - List of agent objects
 * @param {string} props.currentAgentId - The currently selected agent's ID
 * @param {() => void} props.handleOnCancel - Handler for canceling the modal
 * @param {() => void} props.fetchAgents - Handler to refresh the agents list
 * @param {boolean} props.showAllTemplates - Whether the explore all templates dialog is open
 * @param {(open: boolean) => void} props.setShowAllTemplates - Handler to set the show all templates dialog state
 * @param {TemplateCardData[]} props.templates - List of template objects
 * @param {(template: TemplateCardData) => void} props.handleTemplateSelect - Handler for selecting a template
 * @param {boolean} props.showAllAgents - Whether the explore all agents dialog is open
 * @param {(open: boolean) => void} props.setShowAllAgents - Handler to set the show all agents dialog state
 * @param {(agentId: string) => void} props.handleDeleteAgent - Handler for deleting an agent
 * @param {(agentData: any, options?: any) => void} props.createAgent - Handler for creating a new agent
 * @param {(params: { agentId: string, agentData: any }, options?: any) => void} props.updateAgent - Handler for updating an existing agent
 */
export default function AgentModals({
  updateAgentModal,
  setUpdateAgentModal,
  selectedTemplate,
  agents,
  currentAgentId,
  handleOnCancel,
  fetchAgents,
  showAllTemplates,
  setShowAllTemplates,
  templates,
  handleTemplateSelect,
  showAllAgents,
  setShowAllAgents,
  handleDeleteAgent,
}: AgentModalsProps) {
  return <>
    <UpdateAgentModal
      agentId={updateAgentModal === "new" ? null : updateAgentModal}
      isModalOpen={!!updateAgentModal}
      onCancel={() => {
        setUpdateAgentModal(null);
        handleOnCancel();
      }}
      initialData={updateAgentModal === "new" ? {
        name: selectedTemplate?.name || "",
        description: selectedTemplate?.description || "",
        language: "english",
        contactNumber: "",
      } : {
        name: agents.find(agent => agent.id === updateAgentModal)?.name || "",
        description: agents.find(agent => agent.id === updateAgentModal)?.description || "",
        language: agents.find(agent => agent.id === updateAgentModal)?.language || "english",
        contactNumber: agents.find(agent => agent.id === updateAgentModal)?.contact_number || "",
      }}
      onSuccess={fetchAgents}
    />

    <ExploreAllDialog
      open={showAllTemplates}
      onClose={() => setShowAllTemplates(false)}
      items={templates}
      onItemSelect={(template) => {
        setShowAllTemplates(false);
        setUpdateAgentModal("new");
        handleTemplateSelect(template);
      }}
      title="Popular Templates"
      type="templates"
    />

    <ExploreAllDialog
      open={showAllAgents}
      onClose={() => setShowAllAgents(false)}
      items={agents.map((agent) => ({
        id: agent.id,
        name: agent.name,
        description: agent.description,
        metricsCount: 0,
        metrics: [],
        moreCount: 0,
        currentAgentId: currentAgentId,
        isCreateCard: false,
        isCurrentAgent: String(agent.id) === currentAgentId
      }))}
      onItemSelect={(agent) => {
        setShowAllAgents(false);
        setUpdateAgentModal(agent.id);
      }}
      onDelete={handleDeleteAgent}
      title="Your Agents"
      type="agents"
    />
  </>;
} 