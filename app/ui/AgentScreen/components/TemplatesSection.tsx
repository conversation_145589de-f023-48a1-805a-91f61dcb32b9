import { Box, Typography, Button } from "@mui/material";
import TemplateCardList from "@/components/TemplateCard/TemplateCardList";
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import styles from "../styles.module.scss";
import { TemplateCardData } from "@/components/TemplateCard/TemplateCard";
import React from "react";

interface TemplatesSectionProps {
  templates: TemplateCardData[];
  onTemplateSelect: (template: TemplateCardData) => void;
  onShowAllTemplates: () => void;
}

/**
 * TemplatesSection displays a list of popular templates and allows users to explore all templates or select one to create a new agent.
 *
 * @param {Object} props - Component props
 * @param {TemplateCardData[]} props.templates - List of template objects to display
 * @param {(template: TemplateCardData) => void} props.onTemplateSelect - Handler for selecting a template
 * @param {() => void} props.onShowAllTemplates - Handler for showing all templates
 */
export default React.memo(function TemplatesSection({
  templates,
  onTemplateSelect,
  onShowAllTemplates,
}: TemplatesSectionProps) {

  const firstThreeTemplates = templates.slice(0, 3);

  return (
    <Box className={styles.templateContainer}>
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        mb:1
      }}>
        <Typography
          variant="h4"
          sx={{
            fontSize: '24px',
            fontWeight: 600,
            color: '#101828',
            fontFamily: 'Plus Jakarta Sans',
          }}
        >
          Popular Templates
        </Typography>
        <Button
          variant="text"
          onClick={onShowAllTemplates}
          endIcon={<KeyboardArrowRightIcon sx={{ fontSize: 12 }} />}
          sx={{
            color: '#7F56D9',
            textTransform: 'none',
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 500,
            fontSize: '16px',
            padding: '8px 16px',
            gap: '2px',
            '&:hover': {
              backgroundColor: 'transparent',
              textDecoration: 'underline',
            },
            '& .MuiButton-endIcon': {
              marginLeft: '2px',
            }
          }}
        >
          Explore All
        </Button>
      </Box>
      <Box className={styles.gridContainer}>
        <TemplateCardList
          templates={firstThreeTemplates}
          onTemplateSelect={onTemplateSelect}
          isTemplateList={true}
        />
      </Box>
    </Box>
  );
}); 