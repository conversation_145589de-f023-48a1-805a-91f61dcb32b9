import axiosInstanceScenarios from "@/utils/axiosInstanceScenarios";
import { useMutation, UseMutationOptions } from "@tanstack/react-query";
import { IUserDto } from "@/app/api/types/userDto";

// Define the expected input type
interface SignUpInput {
  email: string;
  password: string;
}

export async function signUpFetcher(data: SignUpInput): Promise<IUserDto> {
  const res = await axiosInstanceScenarios.post('/signup', data);
  return res.data;
}

export function useSignUpMutation(
  options?: UseMutationOptions<IUserDto, any, SignUpInput>
) {
  return useMutation<IUserDto, any, SignUpInput>({
    mutationFn: signUpFetcher,
    ...options,
  });
}