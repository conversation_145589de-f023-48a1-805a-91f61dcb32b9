import { usePathname } from "next/navigation";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import useLogin from "@/hooks/useLogin";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";
import { useSignUpMutation } from "./fetcher";

export const useSignUp = () => {
  const analytics = useRudderStackAnalytics();
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  // const router = useRouter();
  const notify = useNotification();
  // const searchParams = useSearchParams();
  const pathname = usePathname();
  const { loginUser } = useLogin();

  interface ISignUpForm {
    email: string;
    password: string;
  }
  // Move mutation instance outside onFinish
  const signUpMutation = useSignUpMutation();
  const onFinish = async (values: ISignUpForm) => {
    analytics?.track("Sign Up", {
      email: values?.email,
      domain: values?.email.split("@")[1],
      app: "test.ai",
      page_name: pathname,
    });

    try {
      // Await the mutation and handle success here
      const data = await signUpMutation.mutateAsync(values);
      notify.success({ message: 'Registration Successful', description: 'You have been successfully registered and logged in.', duration: 3 });
      analytics?.track("Signup_Completed", {
        email: values?.email,
        domain: values?.email.split("@")[1],
        user_id: data.id,
        app: "test.ai",
        marketing_campaign_id: "",
        signup_method: "email",
        page_name: pathname,
      });

      await new Promise(resolve => setTimeout(resolve, 2000));
      await loginUser({ email: values.email, password: values.password });
    } catch (error: any) {
      notify.error({ message: 'Signup failed', description: error?.response?.data?.detail || 'An unexpected error occurred' });
      generalAnalyticsEvents.trackApiRequestFailed(
        'Signup failed',
        error?.response?.status ? String(error.response.status) : '500',
        error?.message || 'Failed to update agent'
      );
    }
  };

  // Use mutation's status to determine loading state
  return { onFinish, loading: signUpMutation.status === 'pending' };
};