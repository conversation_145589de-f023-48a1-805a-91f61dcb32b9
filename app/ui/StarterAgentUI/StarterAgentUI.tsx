"use client";
import React from "react";
import { Box } from "@mui/material";
import Hero from "@/components/Hero/Hero";
import SingleSelectButtonGroup from "@/components/SingleSelectButtonGroup/SingleSelectButtonGroup";
import TemplateCardList from "@/components/TemplateCard/TemplateCardList";
import { TemplateCardData } from "@/components/TemplateCard/TemplateCard";
import { TemplateCardItems } from "./mock-data";
import UpdateAgentModal from "@/app/features/Agent/components/UpdateAgentModal/UpdateAgentModal";
export interface AgentsUIProps {
  onRunTest: () => void;
  onTemplateSelect: (data: TemplateCardData) => void;
  onCancel: () => void;
  testButtonText: string;
  selectedTemplate: TemplateCardData | null;
  updateAgent: string | null;
  items?: TemplateCardData[];
  hideActions?: boolean;
}

export default function AgentsUI({
  onRunTest,
  onTemplateSelect,
  onCancel,
  testButtonText,
  selectedTemplate,
  updateAgent,
  items = TemplateCardItems,
  hideActions = true,
}: AgentsUIProps) {
  return (
    <Box sx={{ mt: 1, mb: 2 }}>
      <Hero onRunTest={onRunTest} testButtonText={testButtonText} />
      <SingleSelectButtonGroup />
      <TemplateCardList
        templates={items}
        onTemplateSelect={onTemplateSelect}
        hideActions={hideActions}
        isTemplateList={true}
      />
      {(selectedTemplate || updateAgent) && (
        <UpdateAgentModal
          agentId={null}
          isModalOpen={true}
          onCancel={onCancel}
          initialData={{
            name: selectedTemplate?.name || "",
            description: selectedTemplate?.description || "",
            language: selectedTemplate ? "English" : "",
            contactNumber: "",
          }}
        />
      )}
    </Box>
  );
}
