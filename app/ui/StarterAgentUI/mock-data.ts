import { TemplateCardData } from "@/components/TemplateCard/TemplateCard";

export const TemplateCardItems: TemplateCardData[] = [
    {
        id: "1",
        name: "Real Estate AI Agent",
        description: "Helps users explore properties, share pricing, and schedule viewings with clear, supportive responses.",
        metricsCount: 6,
        metrics: [
            "Answer from my list of real estate",
            "Completes the call",
            "Shares pricing info"
        ],
        moreCount: 5
    },
    {
        id: "2",
        name: "Customer Support AI Agent",
        description: "Resolves customer issues, answers FAQs, and handles escalations with a calm, helpful tone.",
        metricsCount: 6,
        metrics: [
            "Answer in less than a second",
            "Completes the call",
            "Escalates when needed"
        ],
        moreCount: 3
    },
    {
        id: "3",
        name: "Booking Hotel AI Agent",
        description: "Assists with hotel bookings, suggests options, and manages changes for a smooth guest experience.",
        metricsCount: 6,
        metrics: [
            "Provides booking reference",
            "Confirms bookings",
            "Handles changes without delay"
        ],
        moreCount: 5
    }
];