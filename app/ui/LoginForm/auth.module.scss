@import "@/styles/variables.module";

.section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.formWrapper {
  width: 440px; // w-96 in Tailwind is 384px
  min-width: 440px;
  height: 100%;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-top: 0px;
  padding-bottom: 34px;
  gap: 24px;
  flex-shrink: 0; // Prevent shrinking

  // Add styles for form elements to prevent shrinking
  & > * {
    flex-shrink: 0;
    width: 100%;
  }

  // Form component
  & > div[component="form"] {
    flex-shrink: 0;
    width: 100%;
  }

  @media (max-width: 768px) {
    width: 384px;
    min-width: 320px;
    max-width: 384px;
    padding: 34px 20px;
  }

  @media (max-width: 480px) {
    width: 100%;
    min-width: 300px;
    padding: 34px 16px;
  }
}

.leftSide {
  display: flex;
  height: 100vh;
  flex-direction: column;
  width: 100%;
  justify-content: center;
}

.footer {
  margin-top: auto;
  text-align: center;
  width: 100%;
}

// Responsive styles
@media (max-width: 1024px) {
  .section {
    justify-content: center;
  }

  .leftSide {
    justify-content: flex-start;
    padding-top: 40px;
  }
}

@media (max-height: 800px) {
  .formWrapper {
    padding: 20px 0;
    gap: 16px;
  }
}
