"use client";
import DashboardSection from "@/components/Dashboard/DashboardSection";
import { Box, CircularProgress } from "@mui/material";
import PageTransition from "../PageTransition/PageTransition";
import { useDashboardStatsQuery } from "@/app/(dashboard)/home/<USER>/useDashboardStatsQuery";
import { useGeneralStore } from "@/providers/general-store-provider";

export default function HomeScreen() {
  const currentAgentId = useGeneralStore((state) => state.currentAgentId);
  const { data: stats, isLoading, error } = useDashboardStatsQuery(currentAgentId);



  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '200px',
        color: 'error.main'
      }}>
        Error loading dashboard data: {error.message}
      </Box>
    );
  }

  return <PageTransition>
    <DashboardSection stats={stats ?? null} />
  </PageTransition>;
}