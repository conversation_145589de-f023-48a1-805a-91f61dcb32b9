export interface Feature {
  id: string;
  title: string;
}

export interface CreditOption {
  credits: number;
  price: number;
}

export interface PricingPlan {
  id: string;
  title: string;
  subtitle?: string;
  price: number | 'Contact Us' | 'Custom';
  period?: string;
  description: string;
  features: Feature[];
  buttonText: string;
  buttonUrl?: string;
  tag?: string;
  isPopular?: boolean;
  gradient?: {
    from: string;
    to: string;
  };
  credit_options?: CreditOption[];
  currency?: string;
  metadata?: {
    isCreditUpdate?: boolean;
  };
}

export interface PricingPlansResponse {
  plans: PricingPlan[];
} 