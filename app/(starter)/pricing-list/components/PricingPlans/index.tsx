import React, { useState } from 'react';
import { Box, Typography, Button } from '@mui/material';
import { PricingPlan } from '../../types';
import PricingCard from '../PricingCard';
import { RocketLaunchOutlined } from '@mui/icons-material';
import CreditsBreakdownDialog from '../CreditsBreakdownDialog';

interface PricingPlansProps {
  plans: PricingPlan[];
  onSelectPlan: (plan: PricingPlan) => void;
  onSubscribe: () => void;
}

const PricingPlans: React.FC<PricingPlansProps> = ({ 
  plans, 
  onSelectPlan,
  onSubscribe 
}) => {
  const [isBreakdownOpen, setIsBreakdownOpen] = useState(false);

  return (
    <Box sx={{ width: '100%', margin: '2rem auto'}}>
      <Box 
        sx={{ 
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 6,
        }}
      >
        <Box>
          <Typography
            variant="h1"
            sx={{
              fontSize: { xs: '30px', md: '36px' },
              fontWeight: 700,
              color: '#101828',
              mb: 1,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              fontFamily: 'Plus Jakarta Sans',
            }}
          >
            Subscribe a New Plan
            <span role="img" aria-label="airplane" style={{ fontSize: '32px' }}>✈️</span>
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: '#667085',
              fontSize: '16px',
              lineHeight: '24px',
              fontFamily: 'Plus Jakarta Sans',
            }}
          >
            We work to make your AI Test more custom.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<RocketLaunchOutlined />}
          onClick={() => setIsBreakdownOpen(true)}
          sx={{
            bgcolor: '#7F56D9',
            color: '#fff',
            borderRadius: '16px',
            textTransform: 'none',
            fontSize: '16px',
            fontWeight: 600,
            px: 3,
            py: 1.5,
            fontFamily: 'Plus Jakarta Sans',
            '&:hover': {
              bgcolor: '#6941C6',
            },
            boxShadow: 'none',
          }}
        >
          Credit breakdown
        </Button>
      </Box>

      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr',
            md: 'repeat(2, 1fr)',
          },
          gap: 4,
          width: '100%',
          bgcolor: '#fff',
          borderRadius: '24px',
          p: { xs: 2, md: 4 },
        }}
      >
        {plans.map((plan) => (
          <Box key={plan.id}>
            <PricingCard
              plan={plan}
              onSelect={(updatedPlan) => {
                if (updatedPlan.metadata?.isCreditUpdate) {
                  onSelectPlan(updatedPlan);
                } else if (plan.title.toLowerCase() === 'premium') {
                  onSubscribe();
                } else {
                  onSelectPlan(updatedPlan);
                }
              }}
            />
          </Box>
        ))}
      </Box>

      <CreditsBreakdownDialog
        open={isBreakdownOpen}
        onClose={() => setIsBreakdownOpen(false)}
      />
    </Box>
  );
};

export default PricingPlans; 