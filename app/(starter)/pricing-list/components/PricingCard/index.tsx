import React, { useState } from 'react';
import { Box, Typography, Button, Select, MenuItem, FormControl, SelectChangeEvent } from '@mui/material';
import { PricingPlan, CreditOption } from '../../types';
import CheckIcon from '@mui/icons-material/Check';
import Link from 'next/link';

interface PricingCardProps {
  plan: PricingPlan;
  onSelect: (plan: PricingPlan) => void;
}

const PricingCard: React.FC<PricingCardProps> = ({ plan, onSelect }) => {
  const hasGradient = plan.gradient;
  const isPremiumPlan = plan.title.toLowerCase() === 'premium';
  
  // Initialize with base plan (500 credits) if available and only for Premium plan
  const [selectedCredits, setSelectedCredits] = useState<CreditOption | null>(
    isPremiumPlan && plan.credit_options ? plan.credit_options[0] : null
  );

  // Get current features based on selected credits (only for Premium plan)
  const getCurrentFeatures = () => {
    if (!isPremiumPlan || !selectedCredits) return plan.features;
    
    return plan.features.map((feature, index) => {
      // Update only the first feature which contains the credit amount
      if (index === 0) {
        return {
          ...feature,
          title: `${selectedCredits.credits} Credits`
        };
      }
      return feature;
    });
  };

  const handleCreditChange = (event: SelectChangeEvent<number>) => {
    const selectedOption = plan.credit_options?.find(
      option => option.credits === event.target.value
    );
    if (selectedOption) {
      setSelectedCredits(selectedOption);
      // Update the plan without triggering the payment dialog
      const updatedPlan = {
        ...plan,
        price: selectedOption.price,
        features: [
          { id: '1', title: `${selectedOption.credits} Credits` },
          ...plan.features.slice(1)
        ],
        metadata: {
          ...plan.metadata,
          isCreditUpdate: true
        }
      };
      onSelect(updatedPlan);
    }
  };

  const currentFeatures = getCurrentFeatures();

  return (
    <Box
      sx={{
        position: 'relative',
        p: 4,
        borderRadius: '24px',
        background: hasGradient
          ? `linear-gradient(135deg, ${plan.gradient?.from} 0%, ${plan.gradient?.to} 100%)`
          : '#FAFAFA',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'all 0.3s ease',
        overflow: 'hidden',
        '&::before': hasGradient ? {
          content: '""',
          position: 'absolute',
          top: 0,
          right: 0,
          width: '100%',
          height: '100%',
          background: 'url("/pricing-plan-vector.svg")',
          backgroundSize: '60%',
          backgroundPosition: 'right top',
          backgroundRepeat: 'no-repeat',
          opacity: 0.8,
          pointerEvents: 'none'
        } : {},
      }}
    >
      {plan.tag && (
        <Typography
          variant="caption"
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            bgcolor: hasGradient ? '#ECEBFF' : '#F9F5FF',
            color: hasGradient ? '#8628DB' : '#7F56D9',
            px: 2,
            py: 0.5,
            borderRadius: '16px',
            fontSize: '14px',
            fontWeight: 500,
          }}
        >
          {plan.tag}
        </Typography>
      )}

      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 1, mb: 2 }}>
          {plan.price === 'Custom' ? (
            <Typography
              variant="h4"
              sx={{
                color: hasGradient ? '#fff' : '#101828',
                fontSize: '16px',
                fontWeight: 500,
                lineHeight: 1.2,
                fontFamily: 'Plus Jakarta Sans',
              }}
            >
              Custom Packaging
            </Typography>
          ) : (
            <>
              <Typography
                variant="h4"
                sx={{
                  color: hasGradient ? '#fff' : '#101828',
                  fontSize: '36px',
                  fontWeight: 700,
                  lineHeight: 1.2,
                  fontFamily: 'Plus Jakarta Sans',
                }}
              >
                {plan.title.toLowerCase() === 'enterprise' 
                  ? plan.price 
                  : `$${isPremiumPlan && selectedCredits ? selectedCredits.price : plan.price}`}
              </Typography>
              {isPremiumPlan && plan.credit_options && (
                <FormControl 
                  variant="standard"
                  sx={{ 
                    minWidth: 120,
                    '.MuiSelect-select': {
                      color: hasGradient ? '#fff' : '#101828',
                      fontSize: '16px',
                      fontFamily: 'Plus Jakarta Sans',
                    },
                    '.MuiInput-underline:before': {
                      borderBottom: 'none'
                    },
                    '.MuiInput-underline:after': {
                      borderBottom: 'none'
                    }
                  }}
                >
                  <Select
                    value={selectedCredits?.credits || ''}
                    onChange={handleCreditChange}
                    sx={{
                      color: hasGradient ? 'rgba(255, 255, 255, 0.7)' : '#667085',
                      '& .MuiSelect-icon': {
                        color: hasGradient ? '#fff' : '#667085'
                      }
                    }}
                  >
                    {plan.credit_options.map((option) => (
                      <MenuItem key={option.credits} value={option.credits}>
                        {option.credits} Credits
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            </>
          )}

          {plan.period && (
            <Typography
              variant="body2"
              sx={{
                color: hasGradient ? 'rgba(255, 255, 255, 0.7)' : '#667085',
                fontSize: '16px',
                fontWeight: 500,
                fontFamily: 'Plus Jakarta Sans',
              }}
            >
              /{plan.period}
            </Typography>
          )}
        </Box>

        <Typography
          variant="h6"
          sx={{
            color: hasGradient ? '#fff' : '#101828',
            fontSize: '30px',
            fontWeight: 800,
            mb: 2,
            fontFamily: 'Plus Jakarta Sans',
          }}
        >
          {plan.title}
        </Typography>
        <Typography
          variant="body2"
          sx={{
            color: hasGradient ? 'rgba(255, 255, 255, 0.7)' : '#667085',
            fontSize: '16px',
            fontFamily: 'Plus Jakarta Sans',
          }}
        >
          {plan.description}
          {/* <Button
            sx={{
              p: 0,
              minWidth: 'auto',
              color: hasGradient ? '#fff' : '#101828',
              textTransform: 'none',
              fontSize: 'inherit',
              fontWeight: '700',
              fontFamily: 'Plus Jakarta Sans',
              '&:hover': {
                background: 'transparent',
              }
            }}
          >
            view more
          </Button> */}
        </Typography>
      </Box>

      <Box sx={{ flexGrow: 1 }}>
        <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
          {currentFeatures.map((feature) => (
            <Box
              component="li"
              key={feature.id}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                fontWeight: 500,
                mb: 3,
                color: hasGradient ? 'rgba(255, 255, 255, 0.9)' : '#344054',
                fontSize: '16px',
                fontFamily: 'Plus Jakarta Sans',
              }}
            >
              <Box
                sx={{
                  width: 20,
                  height: 20,
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: hasGradient ? 'rgba(255, 255, 255, 0.1)' : '#F9F5FF',
                }}
              >
                <CheckIcon
                  sx={{
                    fontSize: 16,
                    color: hasGradient ? '#fff' : '#7F56D9'
                  }}
                />
              </Box>
              {feature.title}
            </Box>
          ))}
        </Box>
      </Box>

      {plan.title.toLowerCase().includes('enterprise') ? (
        <Link 
          href="https://calendly.com/has-cntxt/30min" 
          target="_blank"
          rel="noopener noreferrer"
          style={{ textDecoration: 'none', width: '100%' }}
        >
          <Button
            variant="contained"
            fullWidth
            sx={{
              py: 2,
              borderRadius: '16px',
              textTransform: 'none',
              fontSize: '16px',
              fontWeight: 600,
              bgcolor: hasGradient ? '#fff' : '#ECEBFF',
              color: hasGradient ? '#7F56D9' : '#8628DB',
              fontFamily: 'Plus Jakarta Sans',
              cursor: 'pointer',
              '&:hover': {
                bgcolor: hasGradient ? '#fff' : '#ECEBFF',
                color: hasGradient ? '#7F56D9' : '#8628DB',
              },
              boxShadow: 'none',
            }}
          >
            {plan.buttonText}
          </Button>
        </Link>
      ) : (
        <Button
          variant="contained"
          fullWidth
          sx={{
            py: 2,
            borderRadius: '16px',
            textTransform: 'none',
            fontSize: '16px',
            fontWeight: 600,
            bgcolor: hasGradient ? '#fff' : '#ECEBFF',
            color: hasGradient ? '#7F56D9' : '#8628DB',
            fontFamily: 'Plus Jakarta Sans',
            cursor: 'pointer',
            '&:hover': {
              bgcolor: hasGradient ? '#f8fafc' : '#6941C6',
              color: hasGradient ? '#7F56D9' : '#8628DB',
            },
            boxShadow: 'none',
          }}
          onClick={() => onSelect(plan)}
        >
          {plan.title.toLowerCase() === 'premium' ? 'Subscribe' : plan.buttonText}
        </Button>
      )}
    </Box>
  );
};

export default PricingCard; 