import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
  Box,
  Stack,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CallIcon from "@mui/icons-material/Call";
import MicIcon from "@mui/icons-material/Mic";
import ChatIcon from "@mui/icons-material/Chat";

interface CreditsBreakdownDialogProps {
  open: boolean;
  onClose: () => void;
}

export default function CreditsBreakdownDialog({
  open,
  onClose,
}: CreditsBreakdownDialogProps) {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="xs"
      sx={{
        "& .MuiPaper-root": {
          borderRadius: 2,
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          pb: 1,
          fontFamily: 'Plus Jakarta Sans',
        }}
      >
        <Typography 
          variant="h6" 
          component="div"
          sx={{
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 600,
            fontSize: '18px',
          }}
        >
          Credits Usage Breakdown
        </Typography>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ ml: 2 }}
          edge="end"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ pt: 2 }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 2,
            borderRadius: 2,
            backgroundColor: "#F9FAFB",
            mb: 2,
          }}
        >
          <Stack direction="row" alignItems="center" spacing={1}>
            <CallIcon sx={{ color: '#7F56D9' }} fontSize="small" />
            <Typography 
              variant="body1"
              sx={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 500,
              }}
            >
              Call Log Monitoring
            </Typography>
          </Stack>
          <Typography 
            variant="body2" 
            sx={{
              color: '#667085',
              fontFamily: 'Plus Jakarta Sans',
            }}
          >
            1 Credit per Call Log
          </Typography>
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 2,
            borderRadius: 2,
            backgroundColor: "#F9FAFB",
            mb: 2,
          }}
        >
          <Stack direction="row" alignItems="center" spacing={1}>
            <MicIcon sx={{ color: '#7F56D9' }} fontSize="small" />
            <Typography 
              variant="body1"
              sx={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 500,
              }}
            >
              Voice Simulation
            </Typography>
          </Stack>
          <Typography 
            variant="body2" 
            sx={{
              color: '#667085',
              fontFamily: 'Plus Jakarta Sans',
            }}
          >
            10 Credits per Minute
          </Typography>
        </Box>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 2,
            borderRadius: 2,
            backgroundColor: "#F9FAFB",
            mb: 2,
          }}
        >
          <Stack direction="row" alignItems="center" spacing={1}>
            <ChatIcon sx={{ color: '#7F56D9' }} fontSize="small" />
            <Typography 
              variant="body1"
              sx={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 500,
              }}
            >
              Text Simulation
            </Typography>
          </Stack>
          <Typography 
            variant="body2" 
            sx={{
              color: '#667085',
              fontFamily: 'Plus Jakarta Sans',
            }}
          >
            3 Credits per Simulation
          </Typography>
        </Box>

        <Typography
          variant="caption"
          sx={{ 
            display: "block", 
            textAlign: "center", 
            mt: 2,
            color: '#667085',
            fontFamily: 'Plus Jakarta Sans',
          }}
        >
          Credits are deducted based on your usage.
        </Typography>
      </DialogContent>
    </Dialog>
  );
} 