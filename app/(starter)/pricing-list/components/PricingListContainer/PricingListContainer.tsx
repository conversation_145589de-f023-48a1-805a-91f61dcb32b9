'use client';

import React from 'react';
import { Box } from '@mui/material';
import { useState } from 'react';
import { PricingPlan, Feature } from '../../types';
import { useAuthStore } from '@/stores/auth-store';
import { usePlans } from '@/hooks/usePlans';
import { Plan } from '@/types/plans';
import { LoadingSpinner } from '@/components/common/LoadingSpinner';
import { ErrorMessage } from '@/components/common/ErrorMessage';
import PricingPlans from '../PricingPlans';
import StripePaymentDialog from '../StripePaymentDialog';

export const transformApiPlanToPricingPlan = (apiPlan: Plan): PricingPlan => {
    const features: Feature[] = apiPlan.features.map((feature: string, index: number) => ({
        id: index.toString(),
        title: feature
    }));

    // Create credit options array including the base plan
    const credit_options = apiPlan.credit_options ? [
        // Add base plan as first option
        {
            credits: 500, // Base credits
            price: apiPlan.base_price
        },
        ...apiPlan.credit_options
    ] : undefined;

    return {
        id: apiPlan.id.toString(),
        title: apiPlan.title,
        description: apiPlan.description || '',
        price: apiPlan.is_custom ? 'Contact Us' : apiPlan.base_price,
        period: apiPlan.period,
        features,
        buttonText: apiPlan.button_text,
        buttonUrl: apiPlan.title.toLowerCase().includes('enterprise') ? 'https://calendly.com/has-cntxt/30min' : undefined,
        tag: apiPlan.tag,
        isPopular: apiPlan.popular,
        gradient: apiPlan.popular ? {
            from: '#7F56D9',
            to: '#9E77ED'
        } : undefined,
        credit_options,
        currency: apiPlan.currency
    };
};

export default function PricingListPage() {
    const { plans: apiPlans, loading, error, refetch } = usePlans();
    const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null);
    const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
    const { user } = useAuthStore((state) => state);

    const plans = apiPlans.map(transformApiPlanToPricingPlan);

    const handlePlanSelect = (plan: PricingPlan) => {
        setSelectedPlan(plan);
    };

    const handleSubscribe = () => {
        if (!user) {
            return;
        }
        setIsPaymentDialogOpen(true);
    };

    if (loading) {
        return <LoadingSpinner fullScreen />;
    }

    if (error) {
        return (
            <ErrorMessage
                message="Failed to load pricing plans. Please try again later."
                onRetry={refetch}
                fullScreen
            />
        );
    }

    return (
        <Box
            sx={{
                minHeight: '100vh',
                width: '100%',
                bgcolor: '#F9FAFB'
            }}
        >
            <PricingPlans
                plans={plans}
                onSelectPlan={handlePlanSelect}
                onSubscribe={handleSubscribe}
            />

            <StripePaymentDialog
                open={isPaymentDialogOpen}
                onClose={() => setIsPaymentDialogOpen(false)}
                selectedPlan={selectedPlan || plans[0]}
            />
        </Box>
    );
}

