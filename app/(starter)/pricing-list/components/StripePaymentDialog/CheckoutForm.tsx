import React, { useEffect, useRef, useState } from "react";
import {
  PaymentElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { Box, <PERSON><PERSON>, Card, Typography } from "@mui/material";
import useTopUpAnalytics from "@/app/(dashboard)/top-up/useTopUpAnalytics";
import { useRouter } from "next/navigation";
import { PricingPlan } from "@/app/(dashboard)/top-up/components/PricingPlans";
import { useAuthStore } from "@/stores/auth-store";
import useUserData from "@/hooks/useUserData";
import StatusDialog, { StatusType } from "@/components/CongratulationsDialog/CongratulationsDialog";

interface ICheckoutFormProps {
  selectedPlan: PricingPlan;
}

const CheckoutForm = ({ selectedPlan }: ICheckoutFormProps) => {
  const router = useRouter();
  const topUpAnalytics = useTopUpAnalytics();
  const stripe = useStripe();
  const elements = useElements();
  const [paymentCompleted, setPaymentCompleted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showStatusDialog, setShowStatusDialog] = useState(false);
  const [dialogStatus, setDialogStatus] = useState<StatusType>('success');
  const [dialogMessage, setDialogMessage] = useState<string>('');
  const startTimeRef = useRef(Date.now());
  const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;
  const { user, setCurrentUserData } = useAuthStore();
  const { getUserData } = useUserData();

  const handleSubscription = async (paymentId: string) => {
    try {
      if (!user?.token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${BASE_URL}/subscriptions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.token}`,
        },
        body: JSON.stringify({
          payment_id: paymentId,
          plan_id: selectedPlan.id
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create subscription');
      }

      return await response.json();
    } catch (error) {
      console.error('Subscription error:', error);
      throw error;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (!stripe || !elements) {
      setDialogStatus('error');
      setDialogMessage('Payment system is not ready. Please try again.');
      setShowStatusDialog(true);
      setIsSubmitting(false);
      return;
    }

    try {
      // Submit the payment form
      const { error: submitError } = await elements.submit();
      if (submitError) {
        setDialogStatus('error');
        setDialogMessage(submitError.message || 'Failed to submit payment form');
        setShowStatusDialog(true);
        setIsSubmitting(false);
        return;
      }

      // Confirm the payment
      const result = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/starter`,
        },
        redirect: "if_required",
      });

      if (result.error) {
        setDialogStatus('error');
        setDialogMessage(result.error.message || 'Payment confirmation failed');
        setShowStatusDialog(true);
        setIsSubmitting(false);
        return;
      }

      // Check if payment was successful
      if (result.paymentIntent && result.paymentIntent.status === 'succeeded') {
        try {
          // Create subscription with the successful payment
          await handleSubscription(result.paymentIntent.id);

          // Fetch and update user data after successful subscription
          const updatedUserData = await getUserData();
          if (updatedUserData) {
            setCurrentUserData({...user, ...updatedUserData});
          }

          setPaymentCompleted(true);
          setDialogStatus('success');
          setDialogMessage('Your subscription has been successfully activated. You\'re now ready to start your journey with TestAI!');
          setShowStatusDialog(true);
          topUpAnalytics.trackSubscriptionUpgraded("Stripe", selectedPlan.title);
          topUpAnalytics.trackCreditPurchaseCompleted(
            selectedPlan.id,
            +selectedPlan.price,
            0,
          );

        } catch (subscriptionError) {
          console.error("Subscription error:", subscriptionError);
          setDialogStatus('error');
          setDialogMessage('Payment was successful but failed to create subscription. Please contact support.');
          setShowStatusDialog(true);
        }
      }
    } catch (error) {
      console.error("Error in handleSubmit:", error);
      setDialogStatus('error');
      setDialogMessage(error instanceof Error ? error.message : 'An unexpected error occurred');
      setShowStatusDialog(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    const handleBeforeUnload = () => {
      if (!paymentCompleted) {
        const timeSpent = Date.now() - startTimeRef.current;
        topUpAnalytics.trackCreditPurchaseAbandoned(
          selectedPlan.id,
          "Top Up Form",
          timeSpent,
        );
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [paymentCompleted, selectedPlan.id, topUpAnalytics]);
  
  return (
    <>
      <StatusDialog
        open={showStatusDialog}
        onClose={() => {
          setShowStatusDialog(false);
          if (dialogStatus === 'success') {
            router.push('/starter');
          }
        }}
        status={dialogStatus}
        message={dialogMessage}
        autoCloseDelay={dialogStatus === 'success' ? 3000 : undefined}
        onAutoClose={() => {
          setShowStatusDialog(false);
          if (dialogStatus === 'success') {
            router.push('/starter');
          }
        }}
      />
      <Card
        sx={{
          p: '32px',
          borderRadius: '12px',
          boxShadow: 'none',
          bgcolor: '#fff',
          border: 'none'
        }}
      >
        <form onSubmit={handleSubmit}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
            <Box>
              <Typography
                variant="h5"
                sx={{
                  mb: 4,
                  color: '#101828',
                  fontFamily: 'Plus Jakarta Sans',
                  fontWeight: 600,
                  fontSize: '18px'
                }}
              >
                Payment Details
              </Typography>
              <Box sx={{
                '& .StripeElement': {
                  fontFamily: 'Plus Jakarta Sans',
                  fontSize: '16px',
                  color: '#101828',
                  '&::placeholder': {
                    color: '#667085',
                  },
                },
                '& .StripeElement--focus': {
                  borderColor: '#7F56D9',
                },
                '& .StripeElement--invalid': {
                  borderColor: '#F04438',
                },
              }}>
                <PaymentElement />
              </Box>
            </Box>
            <Button
              type="submit"
              variant="contained"
              fullWidth
              disabled={!stripe || !elements || isSubmitting}
              sx={{
                height: '44px',
                fontSize: '16px',
                fontWeight: 600,
                fontFamily: 'Plus Jakarta Sans',
                backgroundColor: '#7F56D9',
                '&:hover': {
                  backgroundColor: '#6941C6',
                },
                borderRadius: '8px',
                textTransform: 'none',
              }}
            >
              {isSubmitting ? 'Processing...' : `Pay $${selectedPlan.price}`}
            </Button>
          </Box>
        </form>
      </Card>
    </>
  );
};

export default CheckoutForm; 