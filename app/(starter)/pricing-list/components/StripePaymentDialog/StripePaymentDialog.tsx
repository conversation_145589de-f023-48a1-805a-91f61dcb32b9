import React from "react";
import { Dialog, DialogContent, DialogTitle, IconButton, Box } from "@mui/material";
import CheckoutForm from "./CheckoutForm";
import { PricingPlan } from "@/app/(dashboard)/top-up/components/PricingPlans";
import CloseIcon from '@mui/icons-material/Close';

interface StripePaymentDialogProps {
    open: boolean;
    onClose: () => void;
    selectedPlan: PricingPlan;
}

const StripePaymentDialog: React.FC<StripePaymentDialogProps> = ({
    open,
    onClose,
    selectedPlan,
}) => {
    return (
        <Dialog
            open={open}
            onClose={onClose}
            fullWidth
            PaperProps={{
                sx: {
                    borderRadius: '16px',
                    p: 0,
                    maxWidth: '1200px',
                    '& .MuiDialogTitle-root': {
                        p: '24px',
                        fontSize: '18px',
                        lineHeight: '28px',
                        fontWeight: 600,
                        fontFamily: 'Plus Jakarta Sans',
                        color: '#101828',
                        borderBottom: '1px solid #EAECF0',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                    },
                    '& .MuiDialogContent-root': {
                        p: '24px',
                        bgcolor: '#F9FAFB'
                    }
                }
            }}
        >
            <DialogTitle>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    Complete Your Subscription
                </Box>
                <IconButton 
                    onClick={onClose}
                    sx={{
                        color: '#667085',
                        '&:hover': {
                            backgroundColor: '#F9FAFB',
                        }
                    }}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent>
                <CheckoutForm selectedPlan={selectedPlan} />
            </DialogContent>
        </Dialog>
    );
};

export default StripePaymentDialog; 