import React, { useState, useEffect } from 'react';
import { loadStripe, StripeElementsOptions } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import { Dialog, DialogTitle, DialogContent, Box, Typography } from '@mui/material';
import { PricingPlan } from '../../types';
import { useAuthStore } from '@/stores/auth-store';
import { PricingPlan as TopUpPricingPlan } from '@/app/(dashboard)/top-up/components/PricingPlans';
import CheckoutForm from './CheckoutForm';
import CreditCardIcon from '@mui/icons-material/CreditCard';

interface IAmountDto {
  id: string;
  clientSecret: string;
  customerId: string;
  options: {
    amount: number;
    currency: string;
    customer: string;
    metadata: {
      intent: string;
      userEmail: string;
      app: string;
    };
  };
}

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || "");

interface StripePaymentDialogProps {
  open: boolean;
  onClose: () => void;
  selectedPlan: PricingPlan;
}

// Helper function to convert PricingPlan to TopUpPricingPlan
const convertToTopUpPlan = (plan: PricingPlan): TopUpPricingPlan => ({
  id: plan.id,
  title: plan.title,
  subtitle: plan.subtitle,
  price: plan.price,
  period: plan.period,
  features: plan.features.map(f => f.title), // Convert Feature[] to string[]
  buttonText: plan.buttonText,
  buttonUrl: plan.buttonUrl,
  tag: plan.tag,
  highlighted: plan.isPopular,
});

const StripePaymentDialog: React.FC<StripePaymentDialogProps> = ({
  open,
  onClose,
  selectedPlan,
}) => {
  const [clientData, setClientData] = useState<IAmountDto | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuthStore((state) => state);

  useEffect(() => {
    const initializePayment = async () => {
      if (open && selectedPlan && !clientData) {
        // Check if the price is a number before proceeding
        if (typeof selectedPlan.price !== 'number') {
          setError('This plan requires contacting sales.');
          setLoading(false);
          return;
        }

        // Check if we have the required environment variable
        const apiUrl = process.env.NEXT_PUBLIC_STRIPE_CREDITS_URL;
        if (!apiUrl) {
          console.error('NEXT_PUBLIC_STRIPE_CREDITS_URL is not defined');
          setError('Payment service configuration is missing');
          setLoading(false);
          return;
        }

        setLoading(true);
        setError(null);
        try {
          const response = await fetch(apiUrl, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${user?.token || ""}`,
            },
            body: JSON.stringify({
              amount: selectedPlan.price,
              email: user?.email,
            } as StripeElementsOptions),
          });

          if (!response.ok) {
            let errorMessage = 'Payment initialization failed';
            try {
              const errorText = await response.text();
              // Only add the error text if it's not HTML
              if (!errorText.includes('<!DOCTYPE')) {
                errorMessage += `: ${errorText}`;
              }
            } catch {
              // If we can't read the error text, just use the default message
            }
            throw new Error(errorMessage);
          }

          const jsonResponse = await response.json();
          if (!jsonResponse.data?.clientSecret) {
            throw new Error('Invalid response from payment service');
          }

          setClientData(jsonResponse.data);
        } catch (error) {
          console.error('Payment initialization error:', error);
          setError(
            error instanceof Error
              ? error.message
              : 'Failed to initialize payment. Please try again.'
          );
        } finally {
          setLoading(false);
        }
      }
    };

    initializePayment();
  }, [open, selectedPlan, user, clientData]);

  // Reset clientData when dialog closes
  useEffect(() => {
    if (!open) {
      setClientData(null);
      setError(null);
    }
  }, [open]);

  const topUpPlan = convertToTopUpPlan(selectedPlan);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      PaperProps={{
        sx: { 
          borderRadius: "12px", 
          width: 1299, 
          backgroundColor: "#FFFFFF", 
          boxShadow: "none",
          border: "none",
          "& .MuiDialogContent-root": {
            border: "none",
            padding: 0
          },
          "& .MuiDialogActions-root": {
            border: "none",
            padding: "16px 24px"
          },
          "& .ant-card": {
            border: "none !important"
          },
          "& .ant-card-bordered": {
            border: "none !important"
          }
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          p: 2,
          border: "none",
          "&.MuiDialogTitle-root": {
            border: "none",
            padding: "16px 24px"
          }
        }}
      >
        <Box display="flex" alignItems="center" gap={1}>
          <Box
            sx={{
              backgroundColor: "#ffff",
              borderRadius: "15px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              padding: "12px",
              boxShadow: "0px 0px 4.89px rgba(159, 159, 159, 0.25)",
              transition: "all 0.3s ease",
            }}
          >
            <CreditCardIcon sx={{ color: '#000', width: 25, height: 25 }} />
          </Box>
          <Typography
            variant="h5"
            sx={{ fontWeight: 700, fontFamily: "Plus Jakarta Sans" }}
          >
            Complete Your Subscription
          </Typography>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ border: "none", padding: 0 }}>
        {loading ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            p: 4,
            gap: 2
          }}>
            <Typography sx={{
              fontFamily: 'Plus Jakarta Sans',
              color: '#344054',
              fontSize: '16px'
            }}>
              Initializing payment...
            </Typography>
          </Box>
        ) : error ? (
          <Box sx={{ p: 4 }}>
            <Typography
              color="error"
              sx={{
                mb: 3,
                fontFamily: 'Plus Jakarta Sans',
                fontSize: '16px',
                color: '#D92D20'
              }}
            >
              {error}
            </Typography>
          </Box>
        ) : clientData ? (
          <Elements
            stripe={stripePromise}
            options={{
              clientSecret: clientData.clientSecret,
              appearance: {
                theme: 'stripe',
                variables: {
                  colorPrimary: '#7F56D9',
                  colorBackground: '#ffffff',
                  colorText: '#101828',
                  colorDanger: '#D92D20',
                  fontFamily: 'Plus Jakarta Sans, sans-serif',
                  fontSizeBase: '16px',
                  spacingUnit: '4px',
                  borderRadius: '8px',
                  colorTextPlaceholder: '#667085',
                },
              },
            }}
          >
            <CheckoutForm selectedPlan={topUpPlan} />
          </Elements>
        ) : null}
      </DialogContent>
    </Dialog>
  );
};

export default StripePaymentDialog; 