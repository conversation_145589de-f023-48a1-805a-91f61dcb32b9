"use client";
import React from "react";
import { Box } from "@mui/material";
import useAgentLogic from "@/hooks/useAgentLogic";
import AgentsUI from "@/app/ui/StarterAgentUI/StarterAgentUI";
import { useQuery } from "@tanstack/react-query";
import { fetchTemplates } from "@/app/features/Agent/lib/fetchers";

export default function StarterScreen() {

  const {
    selectedTemplate,
    updateAgent,
    handleTemplateSelect,
    handleOnCancel,
    handleSelectChange,
  } = useAgentLogic();

  const { data: templates } = useQuery({
    queryKey: ['templates'],
    queryFn: fetchTemplates,
  });

  return (
    <Box sx={{ mt: 2 , mb: 2 }}>
      <Box sx={{ mb: 4 }}>
        <AgentsUI
          testButtonText="Run My First Test"
          onRunTest={handleSelectChange}
          onTemplateSelect={handleTemplateSelect}
          onCancel={handleOnCancel}
          selectedTemplate={selectedTemplate}
          updateAgent={updateAgent}
          hideActions={true}
          items={templates}
        />
      </Box>
    </Box>
  );
}
