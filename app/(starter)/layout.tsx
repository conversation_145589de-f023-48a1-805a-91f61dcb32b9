"use client";

import "@/styles/globals.scss";
import { ReactNode } from "react";
import { notification } from "antd";
import ReactQueryProvider from "@/providers/ReactQueryProvider";
import DashboarWithoutSideNavLayout from "@/layouts/DashboardWithoutSideNav";
import { Box } from "@mui/material";
import AuthGuard from "@/components/Guards/AuthGuard";
import SubscriptionGuard from "@/components/Guards/SubscriptionGuard";

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  const [, contextHolder] = notification.useNotification();

  return (
    <ReactQueryProvider>
      <AuthGuard>
        <SubscriptionGuard>
            <Box sx={{
              maxWidth: '1440px',
              margin: '0 auto',
              width: '100%',
              height: '100%'
            }}>
              <DashboarWithoutSideNavLayout>{children}</DashboarWithoutSideNavLayout>
            </Box>
            {contextHolder}
        </SubscriptionGuard>
      </AuthGuard>
    </ReactQueryProvider>
  );
}
