import { NextRequest, NextResponse } from "next/server"

// External login service base URL
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS!

export async function POST(req: NextRequest) {
  try {
    // Forward credentials to the external login endpoint
    const requestData = await req.json()
    const externalRes = await fetch(`${BASE_URL}/login`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestData),
    });

    
    const responseBody = await externalRes.json();
    console.log("Response body:", responseBody);

    // Handle errors from external service
    if (!externalRes.ok) {
      console.error(`Login error: ${externalRes.status}`)
      return NextResponse.json(
        { ...responseBody, ok: false },
        { status: externalRes.status }
      )
    }

    // On success, set an HttpOnly cookie with the access token
    const { access_token, token_type, access_token_expires } = responseBody
    const res = NextResponse.json({ access_token, token_type, access_token_expires })
    res.cookies.set({
      name: "token",
      value: access_token,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      path: "/",
      maxAge: Number(access_token_expires), // in seconds
    })
    return res
  } catch (error) {
    console.error("Login proxy failed:", error)
    return NextResponse.json(
      {
        access_token: "",
        token_type: "",
        access_token_expires: "",
        error: "Failed to connect to the service",
        ok: false,
      },
      { status: 500 }
    )
  }
}