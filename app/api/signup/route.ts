import { NextRequest, NextResponse } from "next/server";
import { IUserDto } from "@/app/api/types/userDto";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;

export async function POST(
  req: NextRequest,
): Promise<NextResponse<IUserDto | { user: string; error: string }>> {
  try {
    const requestData = await req.json();
    const response = await fetch(`${BASE_URL}/signup`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        {
          user: "",
          error: `HTTP error! status: ${response.status}`,
        },
        {
          status: response.status,
        },
      );
    }

    const responseBody: IUserDto = await response.json();
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error("Failed to post question:", error);
    return NextResponse.json({
      user: "",
      error: "Failed to connect to the service",
    });
  }
}
