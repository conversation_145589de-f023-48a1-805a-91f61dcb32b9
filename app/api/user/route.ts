import { NextRequest, NextResponse } from "next/server";
import { IUserDto } from "@/app/api/types/userDto";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;

// Get Users
export async function GET(
  req: NextRequest,
): Promise<NextResponse<IUserDto | { error: string }>> {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { user: "", error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const response = await fetch(`${BASE_URL}/user`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
    });

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        {
          error: `HTTP error! status: ${response.status}`,
        },
        {
          status: response.status,
        },
      );
    }

    const responseBody: IUserDto = await response.json();
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error("Failed to get user:", error);
    return NextResponse.json(
      {
        error: "Failed to connect to the service",
      },
      { status: 500 },
    );
  }
}
