import { IMetricDto } from "@/app/api/types/metricDto";

export interface IScenarioDto {
  title: string;
  instruction: string;
  personality: string;
  id: number;
  prompt: string;
  metrics: IMetricDto[];
}

export interface IAddNewScenarioDto {
  title: string;
  instruction: string;
  personality: string;
}

export interface IEditScenarioDto {
  title: string;
  instruction: string;
  personality: string;
  id: number;
  prompt: string;
  metrics: string[];
}
