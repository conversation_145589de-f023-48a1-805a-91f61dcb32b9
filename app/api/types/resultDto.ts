export interface IResultDto {
    id: number;
    scenario_name: string;
    timestamp: string;
    duration: string;
    transcription_status: string;
    recording_url: string;
    transcription_url: string;
    call_id: string;
    metrics: Record<string, number>[] | null;
    transcription: Record<string, number> | null;
    end_reason: string;
    latency: number;
    status: string;
    score: number;
}

