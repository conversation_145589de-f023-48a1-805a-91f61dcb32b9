import { NextRequest, NextResponse } from "next/server";
import { IUser } from "@/types/user";
import { IUserDto } from "@/app/api/types/userDto";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;

export async function GET(
  req: NextRequest,
): Promise<NextResponse<{ users: IUser[] }>> {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { users: [], error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const response = await fetch(`${BASE_URL}/admin/users`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
    });

    console.log("response", response);
    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        { users: [], error: `HTTP error! status: ${response.status}` },
        { status: response.status },
      );
    }

    const responseBody: IUserDto[] = await response.json();

    return NextResponse.json({
      users: responseBody.map((user: IUserDto) => ({
        active: user.active,
        call_fact: user.call_fact,
        call_limit: user.call_limit,
        company_name: user.company_name,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        id: String(user.id),
        isAdministrator: false,
      })),
    });
  } catch (error) {
    console.error("Failed to get users:", error);
    return NextResponse.json(
      { users: [], error: "Failed to connect to the service" },
      { status: 500 },
    );
  }
}
