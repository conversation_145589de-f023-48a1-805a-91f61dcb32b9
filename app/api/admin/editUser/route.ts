import { NextRequest, NextResponse } from "next/server";
import { IUserDto } from "@/app/api/types/userDto";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;

export async function PUT(
  req: NextRequest,
): Promise<NextResponse<IUserDto | { user: string; error: string }>> {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { user: "", error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const requestData: IUserDto = await req.json();
    const response = await fetch(`${BASE_URL}/admin/users/${requestData.id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        { user: "", error: `HTTP error! status: ${response.status}` },
        { status: response.status },
      );
    }

    const responseBody: IUserDto = await response.json();
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error("Failed to put user:", error);
    return NextResponse.json({
      user: "",
      error: "Failed to connect to the service",
    });
  }
}
