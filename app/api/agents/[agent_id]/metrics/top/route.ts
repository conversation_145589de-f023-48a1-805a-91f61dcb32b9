import { NextRequest, NextResponse } from "next/server";
import { IMetricDto } from "@/app/api/types/metricDto";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;

type Params = {
  agent_id: string;
};

export async function GET(
  req: NextRequest,
  ctx: { params: Params },
): Promise<NextResponse<IMetricDto[] | { error: string }>> {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { user: "", error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const agent_id = ctx.params.agent_id;
    const response = await fetch(`${BASE_URL}/agents/${agent_id}/metrics/top`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
    });

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json({
        error: `HTTP error! status: ${response.status}`,
      });
    }

    const responseBody: IMetricDto[] = await response.json();
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error("Failed to get the top metrics:", error);
    return NextResponse.json({
      error: "Failed to connect to the service to get the top metrics",
    });
  }
}
