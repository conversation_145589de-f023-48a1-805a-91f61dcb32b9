import { NextRequest, NextResponse } from "next/server";
import { IAddNewMetricDto, IMetricDto } from "@/app/api/types/metricDto";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;

type Params = {
  agent_id: string;
  metric_id: string;
};

export async function PUT(
  req: NextRequest,
  ctx: { params: Params },
): Promise<NextResponse<IMetricDto | { error: string }>> {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { user: "", error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const agent_id = ctx.params.agent_id;
    const metric_id = ctx.params.metric_id;
    const requestData: IAddNewMetricDto = await req.json();
    const response = await fetch(
      `${BASE_URL}/agents/${agent_id}/metrics/${metric_id}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: authorization,
        },
        body: JSON.stringify(requestData),
      },
    );

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        {
          error: `HTTP error! status: ${response.status}`,
        },
        {
          status: response.status,
        },
      );
    }

    const responseBody: IMetricDto = await response.json();
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error("Failed to put metric:", error);
    return NextResponse.json({
      error: "Failed to connect to the service",
    });
  }
}

export async function DELETE(
  req: NextRequest,
  ctx: { params: Params },
): Promise<NextResponse<{ error: string }>> {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { user: "", error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const agent_id = ctx.params.agent_id;
    const metric_id = ctx.params.metric_id;
    const response = await fetch(
      `${BASE_URL}/agents/${agent_id}/metrics/${metric_id}`,
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: authorization,
        },
      },
    );

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        {
          error: `HTTP error! status: ${response.status}`,
        },
        {
          status: response.status,
        },
      );
    }

    return NextResponse.json({
      error: "",
    });
  } catch (error) {
    console.error("Failed to delete metric:", error);
    return NextResponse.json({
      error: "Failed to connect to the service",
    });
  }
}
