import { NextRequest, NextResponse } from "next/server";
import { IAddNewMetricDto, IMetricDto } from "@/app/api/types/metricDto";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;

type Params = {
  agent_id: string;
};

export async function GET(
  req: NextRequest,
  ctx: { params: Params },
): Promise<NextResponse<IMetricDto[] | { error: string }>> {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { user: "", error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const agent_id = ctx.params.agent_id;
    const response = await fetch(`${BASE_URL}/agents/${agent_id}/metrics`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
    });

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        {
          error: `HTTP error! status: ${response.status}`,
        },
        {
          status: response.status,
        },
      );
    }

    const responseBody: IMetricDto[] = await response.json();
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error("Failed to get metrics:", error);
    return NextResponse.json({
      error: "Failed to connect to the service",
    });
  }
}

export async function POST(
  req: NextRequest,
  ctx: { params: Params },
): Promise<NextResponse<IMetricDto | { error: string }>> {
  try {
    const authorization = req.headers.get("Authorization");
    if (!authorization) {
      return NextResponse.json(
        { error: "Missing Authorization header" },
        { status: 401 }
      );
    }

    const agent_id = ctx.params.agent_id;
    const requestData: IAddNewMetricDto = await req.json();
    const response = await fetch(`${BASE_URL}/agents/${agent_id}/metrics`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
      body: JSON.stringify(requestData),
    });

    // Parse the backend response body
    const responseBody = await response.json();

    if (!response.ok) {
      // Forward backend error and status
      return NextResponse.json(
        { error: responseBody.error || `HTTP error! status: ${response.status}` },
        { status: response.status }
      );
    }

    // Success: forward backend response
    return NextResponse.json(responseBody, { status: 200 });
  } catch (error) {
    console.error("Failed to post metric:", error);
    return NextResponse.json(
      { error: "Failed to connect to the service" },
      { status: 500 }
    );
  }
}
