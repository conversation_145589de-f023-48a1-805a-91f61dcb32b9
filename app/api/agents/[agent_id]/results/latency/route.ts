import { NextRequest, NextResponse } from "next/server";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_EVALUATION;

type Params = {
  agent_id: string;
};

export async function GET(req: NextRequest, ctx: { params: Params }) {
  try {
    const authorization = req.headers.get("Authorization");
    if (!authorization) {
      return NextResponse.json(
        { error: "Missing Authorization header", ok: false },
        { status: 401 }
      );
    }

    const agent_id = ctx.params.agent_id;
    const response = await fetch(`${BASE_URL}/agents/${agent_id}/results/latency`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `HTTP error! status: ${response.status}`, ok: false },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Failed to get latency data:", error);
    return NextResponse.json(
      { error: "Failed to connect to the service", ok: false },
      { status: 500 }
    );
  }
}
