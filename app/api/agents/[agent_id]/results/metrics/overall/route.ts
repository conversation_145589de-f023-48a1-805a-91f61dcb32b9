import { NextRequest, NextResponse } from "next/server";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;

type Params = {
  agent_id: string;
};

export async function GET(
  req: NextRequest,
  ctx: { params: Params },
) {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const agent_id = ctx.params.agent_id;
    const response = await fetch(`${BASE_URL}/agents/${agent_id}/metrics/overall?limit=100`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
    });

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        {
          error: `HTTP error! status: ${response.status}`,
        },
        {
          status: response.status,
        },
      );
    }

    const responseBody = await response.json();
    console.log("responseBody", responseBody);
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error("Failed to get overall metrics:", error);
    return NextResponse.json({
      error: "Failed to connect to the service",
    });
  }
} 