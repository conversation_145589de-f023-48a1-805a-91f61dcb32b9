import { NextRequest, NextResponse } from "next/server";
import { IRun } from "@/types/runs";
import { IResultDto } from "@/app/api/types/resultDto";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_EVALUATION;

type Params = {
  agent_id: string;
};

export async function GET(req: NextRequest, ctx: { params: Params }) {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { error: "Missing Authorization header", ok: false },
        { status: 401 },
      );
    }

    const agent_id = ctx.params.agent_id;

    const response = await fetch(`${BASE_URL}/agents/${agent_id}/results`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
    });

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        {
          error: `HTTP error! status: ${response.status}`,
          status: response.status,
          ok: false,
        },
        { status: response.status },
      );
    }

    const responseBody: IResultDto[] = await response.json();
    return NextResponse.json(
      responseBody.map(
        (result) =>
          ({
            id: result.id,
            scenarioName: result.scenario_name,
            timestamp: result.timestamp,
            duration: result.duration,
            transcriptionStatus: result.transcription_status,
            recordingUrl: result.recording_url,
            transcriptionUrl: result.transcription_url,
            callId: result.call_id,
            metrics: result.metrics,
            transcription: result.transcription,
            endReason: result.end_reason,
            latency: result.latency,
            status: result.status,
            score: result.score,
          }) as unknown as IRun,
      ) as IRun[],
    );
  } catch (error) {
    console.error("Failed to get results:", error);
    return NextResponse.json(
      {
        error: "Failed to connect to the service",
        ok: false,
      },
      { status: 500 },
    );
  }
}
