import { NextRequest, NextResponse } from "next/server";
import { IEditScenarioDto, IScenarioDto } from "@/app/api/types/scenarioDto";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;

type Params = {
  agent_id: string;
  scenario_id: string;
};

export async function PUT(
  req: NextRequest,
  ctx: { params: Params },
): Promise<NextResponse<IScenarioDto | { error: string }>> {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { user: "", error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const agent_id = ctx.params.agent_id;
    const scenario_id = ctx.params.scenario_id;
    const requestData: IEditScenarioDto = await req.json();
    const response = await fetch(
      `${BASE_URL}/agents/${agent_id}/scenarios/${scenario_id}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: authorization,
        },
        body: JSON.stringify(requestData),
      },
    );

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        {
          error: `HTTP error! status: ${response.status}`,
        },
        {
          status: response.status,
        },
      );
    }

    const responseBody: IScenarioDto = await response.json();
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error("Failed to put scenario:", error);
    return NextResponse.json({
      error: "Failed to connect to the service",
    });
  }
}
