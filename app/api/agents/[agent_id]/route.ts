// Update Agent

import { NextRequest, NextResponse } from "next/server";
import { IAgentDto, ICreateEditAgentDto } from "@/app/api/types/agentDto";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;

type Params = {
  agent_id: string;
};

export async function PUT(
  req: NextRequest,
  ctx: { params: Params },
): Promise<NextResponse<IAgentDto | { error: string }>> {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { user: "", error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const agent_id = ctx.params.agent_id;
    const requestData: ICreateEditAgentDto = await req.json();
    const response = await fetch(`${BASE_URL}/agents/${agent_id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        {
          error: `HTTP error! status: ${response.status}`,
        },
        {
          status: response.status,
        },
      );
    }

    const responseBody: IAgentDto = await response.json();
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error("Failed to put agent:", error);
    return NextResponse.json({
      error: "Failed to connect to the service",
    });
  }
}

// Delete Agent
export async function DELETE(
  req: NextRequest,
  ctx: { params: Params },
): Promise<NextResponse<{ success: boolean } | { error: string }>> {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const agent_id = ctx.params.agent_id;
    const response = await fetch(`${BASE_URL}/agents/${agent_id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
    });

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        {
          error: `HTTP error! status: ${response.status}`,
        },
        {
          status: response.status,
        },
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to delete agent:", error);
    return NextResponse.json({
      error: "Failed to connect to the service",
    });
  }
}
