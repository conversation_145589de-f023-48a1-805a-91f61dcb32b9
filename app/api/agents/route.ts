import { NextRequest, NextResponse } from "next/server";
import { IAgentDto, ICreateEditAgentDto } from "@/app/api/types/agentDto";
import { Params } from "next/dist/shared/lib/router/utils/route-matcher";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;

// Get Agents
export async function GET(
  req: NextRequest,
): Promise<NextResponse<IAgentDto[] | { error: string }>> {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { user: "", error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const response = await fetch(`${BASE_URL}/agents`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
    });

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        {
          error: `HTTP error! status: ${response.status}`,
        },
        {
          status: response.status,
        },
      );
    }

    const responseBody: IAgentDto[] = await response.json();
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error("Failed to get agents:", error);
    return NextResponse.json({
      error: "Failed to connect to the service",
    });
  }
}

// Add New Agent
export async function POST(
  req: NextRequest,
): Promise<NextResponse<IAgentDto | { error: string }>> {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { user: "", error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const requestData: ICreateEditAgentDto = await req.json();
    const response = await fetch(`${BASE_URL}/agents`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
      body: JSON.stringify(requestData),
    });

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        {
          error: `HTTP error! status: ${response.status}`,
        },
        {
          status: response.status,
        },
      );
    }

    const responseBody: IAgentDto = await response.json();
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error("Failed to post agent:", error);
    return NextResponse.json({
      error: "Failed to connect to the service",
    });
  }
}

export async function DELETE(
  req: NextRequest,
  ctx: { params: Params },
): Promise<NextResponse<{ success: boolean } | { error: string }>> {
  // ...authorization check...
  const authorization = req.headers.get("Authorization");
  const agent_id = ctx.params.agent_id;
  await fetch(`${BASE_URL}/agents/${agent_id}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: authorization || "",
    },
  });
  // ...error handling...
  return NextResponse.json({ success: true });
}
