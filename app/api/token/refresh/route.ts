import { NextRequest, NextResponse } from "next/server";

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;

interface ITokenDto {
  access_token: string;
  token_type: string;
  access_token_expires: string;
  admin: boolean;
}

// Refresh token
export async function GET(
  req: NextRequest,
): Promise<NextResponse<ITokenDto | { error: string }>> {
  try {
    const authorization = req.headers.get("Authorization");

    if (!authorization) {
      return NextResponse.json(
        { user: "", error: "Missing Authorization header" },
        { status: 401 },
      );
    }

    const response = await fetch(`${BASE_URL}/token/refresh`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: authorization,
      },
    });

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return NextResponse.json(
        {
          error: `HTTP error! status: ${response.status}`,
        },
        {
          status: response.status,
        },
      );
    }

    const responseBody: ITokenDto = await response.json();
    return NextResponse.json(responseBody);
  } catch (error) {
    console.error("Failed to refresh token:", error);
    return NextResponse.json({
      error: "Failed to connect to the service",
    });
  }
}
