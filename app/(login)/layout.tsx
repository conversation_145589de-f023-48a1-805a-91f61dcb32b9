"use client";
import { ReactNode, memo, useMemo, useState } from "react";
import "@/styles/globals.scss";
import { NotificationProvider } from "@/context/NotificationContext/NotificationContext";
import { COMPANY_LOGOS } from "@/constants/logos";
import TestimonialCarousel from "@/components/TestimonialCarousel/TestimonialCarousel";
import { TESTIMONIAL_DATA } from "@/constants/testimonial";
import styles from "./layout.module.scss";
import { Box } from "@mui/material";
import PageTransition from "@/app/ui/PageTransition/PageTransition";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
const MemoizedTestimonialCarousel = memo(TestimonialCarousel);

const InnerLoginLayout = memo(({ children }: { children: ReactNode }) => {
  return <NotificationProvider>{children}</NotificationProvider>;
});

InnerLoginLayout.displayName = "InnerLoginLayout";

export default function LoginLayout({ children }: { children: ReactNode }) {
  const [queryClient] = useState(() => new QueryClient());

  const testimonialProps = useMemo(
    () => ({
      testimonials: TESTIMONIAL_DATA,
      logos: COMPANY_LOGOS,
    }),
    [],
  );

  return (
    <div className={styles.section}>
      <QueryClientProvider client={queryClient}>
        <Box className={styles.leftSide}>
          <NotificationProvider>
            <PageTransition>
              {children}
            </PageTransition>
          </NotificationProvider>
        </Box>
        <Box className={styles.rightSide}>
          <MemoizedTestimonialCarousel {...testimonialProps} />
        </Box>
      </QueryClientProvider>

    </div>
  );
}
