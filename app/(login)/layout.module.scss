@import "../../styles/variables.module.scss";

.section {
  align-items: center;
  justify-content: space-between;
  display: grid;
  height: 100%;
  grid-template-columns: 1fr 1fr;
  position: relative;
  background-color: $background-white;
}

.leftSide {
  display: flex;
  height: 100vh;
  flex-direction: column;
}

.rightSide {
  display: flex;
  height: 100vh;
  width: 100%;
  flex-direction: column;
  background-color: #fafafa;
  position: relative;
  overflow: hidden;
}

@media (max-width: 1024px) {
  .section {
    grid-template-columns: 1fr;
  }

  .rightSide {
    display: none;
  }
}
