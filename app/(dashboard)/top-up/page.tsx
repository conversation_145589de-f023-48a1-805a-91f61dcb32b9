"use client";

import { Elements } from "@stripe/react-stripe-js";
import CheckoutForm from "@/components/TopUpForm";
import { loadStripe, StripeElementsOptions } from "@stripe/stripe-js";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Flex } from "antd";
import { useAuthStore } from "@/stores/auth-store";
import PricingPlans, {
  plans as pricingPlans,
  PricingPlan,
} from "./components/PricingPlans";
import styles from "./styles.module.scss";
import useTopUpAnalytics from "@/app/(dashboard)/top-up/useTopUpAnalytics";

interface IAmountDto {
  id: string;
  clientSecret: string;
  customerId: string;
  options: {
    amount: number;
    currency: string;
    customer: string;
    metadata: {
      intent: string;
      userEmail: string;
      app: string;
    };
  };
}

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || "");

export default function TopUpPage() {
  const topUpAnalytics = useTopUpAnalytics();

  const [clientData, setClientData] = useState<IAmountDto | null>(null);
  const { user } = useAuthStore((state) => state);
  const [selectedPlan, setSelectedPlan] = useState<PricingPlan>(
    pricingPlans[1],
  );

  const handlePlanSelect = (plan: PricingPlan) => {
    topUpAnalytics.trackCreditPackageSelected(plan.id, 0, +plan.price, "USD");
    setSelectedPlan(plan);
  };

  const handleProceed = async () => {
    topUpAnalytics.trackSubscriptionUpgradeClicked(selectedPlan.title);
    if (!selectedPlan) return;
    const res = await fetch(process.env.NEXT_PUBLIC_STRIPE_CREDITS_URL || "", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${user?.token || ""}`,
      },
      body: JSON.stringify({
        amount: selectedPlan.price,
        email: user?.email,
      } as StripeElementsOptions),
    });

    const { data } = await res.json();
    setClientData(data);
  };

  useEffect(() => {
    topUpAnalytics.trackSubscriptionPageViewed();
  }, []);

  return (
    <>
      {!clientData ? (
        <Flex vertical gap={24} align="center">
          <PricingPlans
            selectedPlan={selectedPlan}
            onSelectPlan={handlePlanSelect}
          />
          <Button
            onClick={handleProceed}
            color="default"
            variant="solid"
            className={styles.button}
            size="large"
          >
            Proceed to Payment
          </Button>
        </Flex>
      ) : (
        <Elements
          stripe={stripePromise}
          options={{
            clientSecret: clientData.clientSecret,
            locale: "en",
          }}
        >
          <CheckoutForm selectedPlan={selectedPlan} />
        </Elements>
      )}
    </>
  );
}
