@import '@/styles/variables.module';

.wrap {
  width: 100%;
  max-width: 1150px;
  padding: 24px 24px !important;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
  gap: 20px;
  width: 100%;
  justify-content: center;
}

.card {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0;
  cursor: pointer;
  transition: box-shadow 0.3s ease;

  .topSection {
    padding: 16px;

    .tag {
      text-align: center;
      font-size: 14px;
      padding: 7px 7px;
      border-radius: 7px;
    }
  }
}

.card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.selected {
  border: 2px solid #000;
}

.cardHeader {
  margin-bottom: 12px;
  height: 3rem;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.subtitle {
  font-size: 0.875rem;
  color: #666;
  margin-top: 4px;
}

.priceSection {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: bold;
}

.period {
  font-size: 0.875rem;
  color: #888;
  margin-left: 4px;
}

.button {
  margin: 0 auto;
  width: 100%;
}

.features {
  list-style: none;
  padding: 16px;
  margin: 0;
  border-top: 1px solid #E4E7EB;
}

.feature {
  font-size: 0.875rem;
  color: #333;
  display: flex;
  gap: .5rem;
  align-items: center;
  margin-bottom: 10px;
}

.checkIcon {
  color: $black;
  font-size: 20px;
}
