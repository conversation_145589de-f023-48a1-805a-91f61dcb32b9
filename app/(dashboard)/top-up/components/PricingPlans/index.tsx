import React, { useEffect } from "react";
import styles from "./styles.module.scss";
import { Button, Flex, Tag } from "antd";
import useTopUpAnalytics from "@/app/(dashboard)/top-up/useTopUpAnalytics";

export interface PricingPlan {
  id: string;
  title: string;
  tag?: string;
  titleTag?: string;
  subtitle?: string;
  price: number | string;
  period?: string;
  features: string[];
  buttonText: string;
  buttonUrl?: string;
  highlighted?: boolean;
}

interface PricingPlansProps {
  selectedPlan: PricingPlan;
  onSelectPlan: (plan: PricingPlan) => void;
}

export const plans: PricingPlan[] = [
  {
    id: "self-serve",
    title: "Self Serve",
    tag: "50 credits for free",
    price: 19.99,
    period: "month",
    features: [
      "500 Credits",
      "2 Agents, 1 Team Member",
      "Upto 3 Concurrent Calls",
      "Downloadable reports",
      "Alerts on production calls",
      "300 Logs",
      "No limit on evaluation metrics",
      "3 days Logs",
    ],
    buttonText: "Select plan",
  },
  {
    id: "pro",
    title: "Pro",
    titleTag: "Popular",
    subtitle: "Per seat",
    tag: "100 credits for free",
    price: 39.99,
    period: "month",
    features: [
      "Everything in Self Serve, +",
      "2k Credits",
      "2 Use cases / 1 Project",
      "Unlimited agents",
      "4 team members",
      "Upto 20 concurrent calls",
      "Dedicated Slack Support",
    ],
    buttonText: "Select Plan",
  },
  {
    id: "enterprise",
    title: "Enterprise",
    subtitle: "For large scale enterprises",
    tag: "Custom Packaging",
    price: "Contact Us",
    features: [
      "Custom Credits",
      "Custom concurrent calls",
      "Custom API access",
      "Compliance",
      "On-prem deployment",
      "Dedicated Slack Support",
      "Custom SSO",
      "Dedicated Support Engineer",
    ],
    buttonText: "Get in touch",
    buttonUrl: "https://calendly.com/has-cntxt/30min",
  },
];

const PricingPlans: React.FC<PricingPlansProps> = ({
  selectedPlan,
  onSelectPlan,
}) => {
  const topUpAnalytics = useTopUpAnalytics();

  useEffect(() => {
    topUpAnalytics.trackCreditPackageViewed(plans.map((plan) => plan.title));
  }, []);
  
  return (
    <Flex vertical gap={36} className={styles.wrap}>
      <p>
        Unlock unlimited AI tests, detailed performance reports, and advanced
        compliance validation.
      </p>
      <div className={styles.grid}>
        {plans.map((plan) => (
          <div
            key={plan.id}
            className={`${styles.card} ${selectedPlan.id === plan.id ? styles.selected : ""}`}
            onClick={() => onSelectPlan(plan)}
          >
            <Flex vertical gap={20} className={styles.topSection}>
              <Flex vertical gap={64}>
                <div className={styles.cardHeader}>
                  <Flex justify="space-between">
                    <h3 className={styles.title}>{plan.title}</h3>
                    {plan.titleTag && (
                      <Tag className={styles.titleTag} color="#000">
                        {plan.titleTag}
                      </Tag>
                    )}
                  </Flex>

                  <p className={styles.subtitle}>{plan.subtitle}</p>
                </div>
                <Flex vertical gap={24}>
                  <Tag color="default" className={styles.tag}>
                    {plan.tag}
                  </Tag>
                  <div className={styles.priceSection}>
                    <span className={styles.price}>
                      {typeof plan.price === "number"
                        ? `$${plan.price}`
                        : plan.price}
                    </span>
                    {plan.period && (
                      <span className={styles.period}>/{plan.period}</span>
                    )}
                  </div>
                </Flex>
              </Flex>
              <Button
                size="large"
                color="default"
                variant="solid"
                className={styles.button}
                onClick={() => {
                  if (plan.buttonUrl) {
                    window.open(
                      plan.buttonUrl,
                      "_blank",
                      "noopener,noreferrer",
                    );
                  } else {
                    onSelectPlan(plan);
                  }
                }}
              >
                {plan.buttonText}
              </Button>
            </Flex>
            <ul className={styles.features}>
              {plan.features.map((feature, index) => (
                <li key={index} className={styles.feature}>
                  <i className={styles.checkIcon + " bx bx-check"}></i>
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </Flex>
  );
};

export default PricingPlans;
