import { useMemo } from "react";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import { useAuthStore } from "@/stores/auth-store";
import { usePathname } from "next/navigation";

const useTopUpAnalytics = () => {
  const analytics = useRudderStackAnalytics();
  const { user } = useAuthStore((state) => state);
  const pathname = usePathname();

  return useMemo(() => {
    const track = (
      eventName: string,
      payload: Record<string, unknown> = {},
    ) => {
      if (!analytics) return;
      analytics.track(eventName, {
        user_id: user?.id,
        userId: user?.id,
        email: user?.email,
        domain: user?.email.split("@")[1],
        timestamp: new Date().toISOString(),
        app: "test.ai",
        category: payload.category ? (payload.category as string) : "Premium",
        page_name: pathname,
        ...payload,
      });
    };

    return {
      // Old events
      trackSubscriptionPageViewed: () => track("Subscription_Page_Viewed"),
      trackSubscriptionUpgradeClicked: (plan_selected: string) =>
        track("Subscription_Upgrade_Clicked", {
          plan_selected,
        }),
      trackSubscriptionUpgraded: (payment_method: string, new_plan: string) =>
        track("Subscription_Upgraded", {
          payment_method,
          new_plan,
        }),
      trackCheckout: () => track("Checkout"),

      // New events
      trackCreditPackageViewed: (packages_displayed: string[]) =>
        track("credit_package_viewed", {
          packages_displayed,
          current_credits: `${user?.call_fact}/${user?.call_limit}`,
        }),

      trackCreditPackageSelected: (
        package_id: string,
        credit_amount: number,
        price: number,
        currency: string,
      ) =>
        track("credit_package_selected", {
          package_id,
          credit_amount,
          price,
          currency,
        }),
      trackPaymentMethodSelected: (
        payment_method: string,
        previous_method: string,
      ) =>
        track("payment_method_selected", {
          payment_method,
          previous_method,
        }),
      trackCreditPurchaseCompleted: (
        package_id: string,
        amount_spent: number,
        credits_purchased: number,
      ) =>
        track("credit_purchase_completed", {
          package_id,
          amount_spent,
          credits_purchased,
        }),
      trackCreditPurchaseAbandoned: (
        package_id: string,
        abandonment_step: string,
        time_spent: number,
      ) =>
        track("credit_purchase_abandoned", {
          package_id,
          abandonment_step,
          time_spent,
        }),
    };
  }, [analytics, user?.id]);
};

export default useTopUpAnalytics;
