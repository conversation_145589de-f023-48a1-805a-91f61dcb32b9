@import '@/styles/variables.module';

.wrap {
  background-color: #F7F8FA;
  margin-top: 1.5rem;
}

.pageTitle {
  font-size: 20px !important;
  font-weight: 600 !important;
  margin-bottom: 24px !important;
  margin-top: 24px !important;
  color: #1C1C1C !important;
  margin: 0 !important;
  line-height: 30px !important;
  font-family: 'Plus Jakarta Sans', sans-serif !important; 

}

.resultsTable {
  background: #FFFFFF !important;
  border-radius: 20px !important;
  box-shadow: 0px 1px 4px rgba(16, 24, 40, 0.06) !important;
  overflow: hidden;
  border: none !important;
  padding: 24px;
  margin-top: 0;
  margin-bottom: 0;

  :global {
    .MuiTable-root {
      border-collapse: separate;
      border-spacing: 0;
    }
    .MuiTableHead-root {
      .MuiTableRow-root {
        font-family: 'Plus Jakarta Sans', sans-serif;
        background: #F8F9FB;
        border-radius: 16px 16px 0 0;
        .MuiTableCell-head {
          font-family: 'Plus Jakarta Sans', sans-serif;
          font-size: 14px;
          font-weight: 500;
          color: #23272E;
          background: #F8F9FB;
          border-bottom: none;
          padding: 20px 24px;
          line-height: 1.4;
          position: relative;
          &:not(:last-child)::after {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 28px;
            background: #ECECEC;
          }
          &:first-child {
            border-radius: 16px 0 0 0;
          }
          &:last-child {
            border-radius: 0 16px 0 0;
          }
        }
      }
    }
    .MuiTableBody-root {
      .MuiTableRow-root {
        transition: background-color 0.2s;
        &:nth-of-type(even) {
          background: #F8F9FB;
        }
        &:nth-of-type(odd) {
          background: #FFF;
        }
        &:hover {
          background: #F3F4F6;
        }
      }
      .MuiTableCell-body {
        padding: 20px 24px;
        font-family: 'Plus Jakarta Sans', sans-serif;
        font-size: 14px;
        font-weight: 500;
        line-height: 18px;
        letter-spacing: 0;
        color: #23272E;
        border-bottom: none;
        vertical-align: middle;
        background: none;
        &:first-child {
          min-width: 180px;
        }
        &:last-child {
          text-align: right;
        }
      }
    }
  }
}

.testId {
  font-size: 17px !important;
  font-weight: 500 !important;
  color: #23272E !important;
  line-height: 1.4 !important;
}

.phoneIconWrapper {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background-color: #F3F4F6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  svg {
    color: #6B7280;
    font-size: 22px;
  }
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  padding: 4px 18px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
  height: 32px;
  background: #E9F9EF;
  color: #3BB77E;
  text-transform: none;
  letter-spacing: 0;
}

.statusCompleted {
  background: #E9F9EF;
  color: #3BB77E;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  height: 32px;
}

.statusFailed {
  background: #FDECEC;
  color: #F04438;
  font-weight: 600;
  line-height: 18px;
  height: 32px;
}

.scoreProgress {
  display: inline-flex;
  position: relative;
  .MuiCircularProgress-root {
    color: #A78BFA !important;
    background: none;
  }
  .MuiTypography-root {
    color: #8B5CF6 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
  }
}

.actionButton {
  width: 40px !important;
  height: 40px !important;
  color: #6B7280 !important;
  &:hover {
    background-color: #F3F4F6 !important;
    color: #23272E !important;
  }
  svg {
    font-size: 24px;
  }
}

// MUI Button overrides
.primaryButton {
  background-color: #8B5CF6 !important;
  color: #FFFFFF !important;
  height: 40px !important;
  padding: 10px 18px !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  text-transform: none !important;
  font-size: 14px !important;
  line-height: 20px !important;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05) !important;

  &:hover {
    background-color: #7C3AED !important;
  }

  svg {
    font-size: 20px;
  }
}

.secondaryButton {
  height: 40px !important;
  padding: 10px 16px !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  border: 1px solid #D0D5DD !important;
  color: #344054 !important;
  text-transform: none !important;
  font-size: 14px !important;
  line-height: 20px !important;
  background-color: #FFFFFF !important;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05) !important;

  &:hover {
    background-color: #F9FAFB !important;
  }

  svg {
    font-size: 20px;
    margin-left: 8px !important;
  }
}

// Empty state styles
.emptyStateWrapper {
  padding: 48px 24px;
  text-align: center;
}

.emptyStateIcon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #F9F5FF;
  margin: 0 auto 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  svg {
    color: #8B5CF6;
    font-size: 24px;
  }
}

.emptyStateTitle {
  color: #101828;
  font-size: 16px;
  font-weight: 600;
  font-family: 'Plus Jakarta Sans', sans-serif;
  margin-bottom: 8px;
}

.emptyStateDescription {
  color: #475467;
  font-size: 14px;
  font-family: 'Plus Jakarta Sans', sans-serif;
  max-width: 420px;
  margin: 0 auto;
}

.metricsCard {
  background: #fff;
  border-radius: 24px;
  padding: 32px 28px;
  margin-bottom: 32px;
  box-shadow: 0px 1px 4px rgba(16, 24, 40, 0.06);
}

.metricsTitle {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #23272E;
  margin-bottom: 0;
}

.metricLabel {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #23272E;
  flex: 1;
  margin-right: 16px;
}

.metricProgressWrap {
  display: flex;
  align-items: center;
  min-width: 160px;
  gap: 16px;
}

.metricProgressBg {
  width: 100px;
  height: 12px;
  background: #F4EBFF;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  margin-right: 8px;
}

.metricProgressBar {
  height: 100%;
  background: #8B5CF6;
  border-radius: 8px;
  position: relative;
  transition: width 0.3s;
  display: flex;
  align-items: center;
}

.metricProgressDot {
  width: 16px;
  height: 16px;
  background: #fff;
  border: 4px solid #8B5CF6;
  border-radius: 50%;
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  box-sizing: border-box;
}

.metricPercent {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #8B5CF6;
  min-width: 36px;
  text-align: right;
}