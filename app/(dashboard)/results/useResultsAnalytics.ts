import { useMemo } from "react";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import { useAuthStore } from "@/stores/auth-store";
import { IRun } from "@/types/runs";
import { usePathname } from "next/navigation";

const useResultsAnalytics = () => {
  const analytics = useRudderStackAnalytics();
  const { user } = useAuthStore((state) => state);
  const pathname = usePathname();

  return useMemo(() => {
    const track = (
      eventName: string,
      payload: Record<string, unknown> = {},
    ) => {
      if (!analytics) return;
      analytics.track(eventName, {
        user_id: user?.id,
        userId: user?.id,
        email: user?.email,
        domain: user?.email.split("@")[1],
        timestamp: new Date().toISOString(),
        app: "test.ai",
        category: payload.category ? (payload.category as string) : "Results",
        page_name: pathname,
        ...payload,
      });
    };

    return {
      trackViewedRunDetails: () => {
        track("Viewed Run Details");
      },
      trackCallDetailsViewed: (run: IRun) => {
        track("call_details_viewed", {
          call_id: run.callId,
          call_duration: run.duration,
          call_status: run.transcriptionStatus,
        });
      },
      trackCallRecordingPlayed: (run: IRun) => {
        track("call_recording_played", {
          call_id: run.callId,
          playback_duration: run.duration,
        });
      },
      trackCallTranscriptViewed: (
        run: IRun,
        scroll_depth: number,
        time_spent: number,
      ) => {
        track("call_transcript_viewed", {
          call_id: run.callId,
          scroll_depth,
          time_spent,
        });
      },
    };
  }, [analytics, user?.id]);
};

export default useResultsAnalytics;
