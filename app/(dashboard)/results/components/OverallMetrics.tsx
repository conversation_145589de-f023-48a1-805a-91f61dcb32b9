import React from 'react';
import { Box, Typography, Stack } from '@mui/material';
import styles from './OverallMetrics.module.scss';

interface Metric {
  name: string;
  value: number;
}

interface OverallMetricsProps {
  metrics: Metric[];
}

const OverallMetrics: React.FC<OverallMetricsProps> = ({ metrics }) => {
  return (
    <Box className={styles.metricsCard}>
      <Typography className={styles.metricsTitle}>Overall Metrics</Typography>
      <Stack spacing={3} mt={3}>
        {metrics.map((metric, idx) => (
          <Stack key={idx} direction="row" alignItems="center" justifyContent="space-between">
            <Typography className={styles.metricLabel}>{metric.name}</Typography>
            <Box className={styles.metricProgressWrap}>
              <Box className={styles.metricProgressBg}>
                <Box
                  className={styles.metricProgressBar}
                  sx={{ width: `${metric.value}%` }}
                >
                  <Box className={styles.metricProgressDot} />
                </Box>
              </Box>
              <Typography className={styles.metricPercent}>{metric.value}%</Typography>
            </Box>
          </Stack>
        ))}
      </Stack>
    </Box>
  );
};

export default OverallMetrics; 