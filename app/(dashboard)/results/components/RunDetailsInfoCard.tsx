import React from 'react';
import styles from './RunDetailsInfoCard.module.scss';

interface RunDetailsInfoCardProps {
  id?: string;
  latency?: number;
  timestamp?: string | number | Date;
}

const RunDetailsInfoCard: React.FC<RunDetailsInfoCardProps> = ({ id, latency, timestamp }) => (
  <div className={styles.infoCard}>
    {/* Test ID */}
    <div className={styles.infoCard__column}>
      <div className={styles.infoCard__label}>Test ID</div>
      <div className={styles.infoCard__value}>{id || '-'}</div>
    </div>
    {/* Divider */}
    <div className={styles.infoCard__divider} />
    {/* Latency */}
    <div className={styles.infoCard__column}>
      <div className={styles.infoCard__label}>Latency</div>
      <div className={styles.infoCard__value}>{typeof latency === 'number' ? `${latency.toFixed(1)} ms` : '-'}</div>
    </div>
    {/* Divider */}
    <div className={styles.infoCard__divider} />
    {/* Created At */}
    <div className={styles.infoCard__column}>
      <div className={styles.infoCard__label}>Created At</div>
      <div className={styles.infoCard__value}>
        {timestamp ? new Date(timestamp).toLocaleString('en-US', { month: '2-digit', day: '2-digit', year: '2-digit', hour: 'numeric', minute: '2-digit', hour12: true }) : '-'}
      </div>
    </div>
  </div>
);

export default RunDetailsInfoCard; 