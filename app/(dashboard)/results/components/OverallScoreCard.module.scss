.overallScoreCard {
  display: flex;
  align-items: center;
  background: #F7F8FA;
  border: 1px solid #E4E7EC;
  border-radius: 12px;
  padding: 10px;
  margin: 0 24px 24px 24px;
  cursor: pointer;
  transition: box-shadow 0.15s, border-color 0.15s, background 0.15s, transform 0.1s;
}

.overallScoreCard:hover {
  box-shadow: 0 2px 8px rgba(134, 40, 219, 0.15);
  border-color: #A855F7;
  background: #F9FAFB;

  .label {
    color: #7C3AED;
  }

  .value {
    color: #7C3AED;
  }

  .arrow svg path {
    stroke: #7C3AED;
  }
}

.overallScoreCard:focus {
  outline: none;
  border-color: #A855F7;
  background: #F9FAFB;
  box-shadow: 0 2px 8px rgba(134, 40, 219, 0.15), 0 0 0 3px rgba(134, 40, 219, 0.1);

  .label {
    color: #7C3AED;
  }

  .value {
    color: #7C3AED;
  }

  .arrow svg path {
    stroke: #7C3AED;
  }
}

.overallScoreCard:active {
  border-color: #A855F7;
  background: #F9FAFB;
  box-shadow: 0 2px 8px rgba(134, 40, 219, 0.2);
  transform: translateY(1px);

  .label {
    color: #7C3AED;
  }

  .value {
    color: #7C3AED;
  }

  .arrow svg path {
    stroke: #7C3AED;
  }
}

.icon {
  width: 28px;
  height: 28px;
  margin-right: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.label {
  font-size: 12px;
  font-weight: 600;
  color: #23272E;
  margin-right: 12px;
  font-family: 'Plus Jakarta Sans', sans-serif;
  transition: color 0.15s;
}

.value {
  font-size: 12px;
  font-weight: 700;
  color: #23272E;
  font-family: 'Plus Jakarta Sans', sans-serif;
  transition: color 0.15s;
}

.spacer {
  flex: 1;
}

.arrow {
  font-size: 24px;
  color: #23272E;
  margin-left: 12px;
  display: flex;
  align-items: center;

  svg path {
    transition: stroke 0.15s;
  }
}