.infoCard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #F7F8FA;
  border-radius: 20px;
  padding: 16px;
  margin: 0 24px 16px 24px;
  min-height: 90px;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.infoCard__column {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.infoCard__label {
  color: #8F96A3;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
  text-align: center;
  letter-spacing: 0.1px;
}

.infoCard__value {
  color: #23272E;
  font-weight: 600;
  font-size: 12px;
  text-align: center;
}

.infoCard__divider {
  width: 2px;
  height: 48px;
  background: #E4E7EC;
  border-radius: 1px;
  margin: 0 24px;
} 