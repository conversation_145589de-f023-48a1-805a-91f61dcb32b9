import React from 'react';
import { Skeleton, Typography } from '@mui/material';
import DashboardBarChart from '@/components/Dashboard/DashboardBarChart';

interface ChartDataPoint {
  month: string;
  year: number;
  total_latency_ms?: number;
}

interface LatencyBarChartProps {
  isLoading: boolean;
  error: Error | null;
  data: ChartDataPoint[];
  visibleMonths: Set<string>;
}

const LatencyBarChart: React.FC<LatencyBarChartProps> = ({ isLoading, error, data, visibleMonths }) => {
  return (
    <>
      {isLoading ? (
        <Skeleton variant="rectangular" height={340} sx={{ borderRadius: '34px', mb: 2 }} />
      ) : error ? (
        <Typography color="error" sx={{ mb: 2 }}>
          Failed to load latency data
        </Typography>
      ) : data && data.length > 0 ? (
        <DashboardBarChart
          data={data}
          title="Overall Latency"
          dataKey="total_latency_ms"
          barColor="#A689FA"
          xAxisTickFormatter={(month) => visibleMonths.has(month) ? month : ''}
        />
      ) : null}
    </>
  );
};

export default LatencyBarChart; 