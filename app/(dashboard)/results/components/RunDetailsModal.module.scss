@import '@/styles/variables.module';


.modalBodyWrapper {
  position: relative;
}

.scrollableContent {
  height: 50vh; 
  overflow-y: auto;
}

.audioPlayerFixed {
  position: absolute;
  left: 0;
  bottom: 10px;
  width: 100%;
  background-color: #fff;
  padding: 10px;
}


.bx {
  font-size: 24px;
  cursor: pointer;
  color: $gray;
}

.numberInput {
  width: 100% !important;
}

.createButton {
  width: 100%;
  height: 40px !important;
  padding: 8px 20px !important;
  border-radius: 4px !important;
}

.select {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;

  .selectIcon {
    font-size: 24px;
    color: $black;
  }
}

.descriptions {
  :global {
    .ant-descriptions-view {
      border: none !important;
    }
  }

  .descriptionsLabel {
    font-size: 14px;
    font-weight: 600;
    line-height: 21px;
    color: $black;
    text-transform: capitalize;
  }

  .descriptionsValue {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    color: $black;
    white-space: pre-wrap;
  }
}

.timestamp {
  display: flex;
  align-items: center;
}