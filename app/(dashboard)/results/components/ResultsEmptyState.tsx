import { Box, Typography} from '@mui/material';
import Image from 'next/image';

const ResultsEmptyState = () => (
  <Box
    sx={{
      width: '100%',
      height: 550,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      bgcolor: '#fff',
      borderRadius: 4,
    }}
  >
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        bgcolor: '#fff',
        p: 4,
        borderRadius: 4,
        minWidth: 400,
      }}
    >
      {/* Icon */}
      <Box
        sx={{
          bgcolor: '#F4EBFF',
          borderRadius: '16px',
          width: 80,
          height: 80,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          mb: 2,
        }}
      >
        <Image src="/book-open-02.svg" alt="Empty Results" width={57} height={57} />
      </Box>
      {/* Title */}
      <Typography variant="h6" fontWeight={700} mb={0.5} color="#101828" align="center" sx={{ fontFamily: 'Plus Jakarta Sans' }}>
        No Results Found
      </Typography>
      {/* Subtitle */}
      <Typography variant="body2" sx={{ fontFamily: 'Plus Jakarta Sans' }} color="#667085" mb={3} align="center">
        There are no results for this agent yet.
      </Typography>
    </Box>
  </Box>
);

export default ResultsEmptyState; 