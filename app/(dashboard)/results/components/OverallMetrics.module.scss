.metricsCard {
  background: #fff;
  border-radius: 24px;
  padding: 32px 28px;
  margin-bottom: 32px;
  height: 100%;
}

.metricsTitle {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 16px !important;
  font-weight: 700 !important;
  color: #23272E;
  margin-bottom: 0;
}

.metricLabel {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 14px !important;
  font-weight: 400;
  color: #23272E;
  flex: 1;
  margin-right: 16px;
}

.metricProgressWrap {
  display: flex;
  align-items: center;
  min-width: 160px;
  gap: 16px;
}

.metricProgressBg {
  width: 100px;
  height: 12px;
  background: #F4EBFF;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  margin-right: 8px;
}

.metricProgressBar {
  height: 100%;
  background: #8B5CF6;
  border-radius: 8px;
  position: relative;
  transition: width 0.3s;
  display: flex;
  align-items: center;
}

.metricProgressDot {
  width: 16px;
  height: 16px;
  background: #fff;
  border: 4px solid #8B5CF6;
  border-radius: 50%;
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  box-sizing: border-box;
}

.metricPercent {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #8B5CF6;
  min-width: 36px;
  text-align: right;
} 