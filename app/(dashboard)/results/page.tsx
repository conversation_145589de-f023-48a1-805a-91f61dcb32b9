"use client";

import React, { useEffect, useState } from "react";
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Paper,
  Stack,
  IconButton,
  Menu,
  MenuItem,
  Skeleton,
} from "@mui/material";
import { useGeneralStore } from "@/providers/general-store-provider";
import { IRun } from "@/types/runs";
import styles from "./styles.module.scss";
import dayjs from "dayjs";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import RunDetailsModal from "./components/RunDetailsModal";
import { useAuthStore } from "@/stores/auth-store";
import { useQuery } from "@tanstack/react-query";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import { usePathname } from "next/navigation";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";
import { MoreV<PERSON>, Phone } from '@mui/icons-material';
import OverallMetrics from './components/OverallMetrics';
import ScoreProgress from './components/ScoreProgress';
import LatencyBarChart from './components/LatencyBarChart';
import ResultsEmptyState from "./components/ResultsEmptyState";

// Add Metric type if not present
type Metric = { name: string; value: number };

const StatusBadge = ({ status }: { status: string }) => {
  let badgeClass = styles.statusCompleted;
  let label = status;
  if (status.toLowerCase() === 'failed') {
    badgeClass = styles.statusFailed;
    label = 'Failed';
  } else if (status.toLowerCase() === 'completed') {
    badgeClass = styles.statusCompleted;
    label = 'Completed';
  }
  return (
    <span className={`${styles.statusBadge} ${badgeClass}`}>{label}</span>
  );
};

const TableSkeleton = () => (
  <>
    {[1, 2, 3].map((row) => (
      <TableRow key={row}>
        {[1, 2, 3, 4, 5, 6, 7].map((cell) => (
          <TableCell key={cell}>
            <Skeleton variant="text" width={cell === 1 ? 150 : 100} height={24} />
          </TableCell>
        ))}
      </TableRow>
    ))}
  </>
);

export default function ResultsPage() {
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const analytics = useRudderStackAnalytics();
  const pathname = usePathname();
  const [isRunDetailsModalOpen, setIsRunDetailsModalOpen] = useState<IRun | null>(null);
  const { currentAgentId } = useGeneralStore((state) => state);
  const { user } = useAuthStore((state) => state);
  const notify = useNotification();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRunForMenu, setSelectedRunForMenu] = useState<IRun | null>(null);

  const handleMenuClick = (event: React.MouseEvent<HTMLButtonElement>, run: IRun) => {
    setAnchorEl(event.currentTarget);
    setSelectedRunForMenu(run);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedRunForMenu(null);
  };


  useEffect(() => {
    if (analytics) {
      analytics.track("Results_Page_Viewed", {
        category: "Simulation",
        email: user?.email,
        domain: user?.email.split("@")[1],
        user_id: user?.id,
        app: "test.ai",
        page_name: pathname,
      });
    }
  }, [pathname, user?.email, user?.id, analytics]);

  const {
    data: runs,
    isLoading: isResultsLoading,
    error: resultsError,
  } = useQuery<IRun[]>({
    queryKey: ["results", currentAgentId],
    queryFn: async () => {
      if (!currentAgentId) throw new Error("No agent selected");
      const response = await fetch(`/api/agents/${currentAgentId}/results`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${user?.token || ""}`,
        },
      });
      if (!response.ok) {
        generalAnalyticsEvents.trackApiRequestFailed(
          "GET results",
          String(response.status),
          "Failed to fetch results",
        );
        throw new Error(`Failed to fetch results: ${response.status}`);
      }
      return response.json();
    },
    enabled: Boolean(currentAgentId && user?.token),
  });

  useEffect(() => {
    if (resultsError) {
      notify.error({
        message: "Failed to get results",
        description: (resultsError as Error).message,
      });
    }
  }, [resultsError, notify]);

  const {
    data: topMetricsData,
    error: topMetricsError,
  } = useQuery<{ name: string; value: number }[]>({
    queryKey: ["topMetrics", currentAgentId],
    queryFn: async () => {
      if (!currentAgentId) throw new Error("No agent selected");
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS;
      const response = await fetch(
        `${baseUrl}/agents/${currentAgentId}/metrics/overall`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${user?.token || ""}`,
          },
        }
      );
      if (!response.ok) {
        generalAnalyticsEvents.trackApiRequestFailed(
          "GET top metrics",
          String(response.status),
          "Failed to fetch top metrics",
        );
        throw new Error(`Failed to fetch top metrics: ${response.status}`);
      }
      return response.json();
    },
    enabled: Boolean(currentAgentId && user?.token),
  });

  useEffect(() => {
    if (topMetricsError) {
      notify.error({
        message: "Failed to get top metrics",
        description: (topMetricsError as Error).message,
      });
    }
  }, [topMetricsError, notify]);

  const columns = [
    {
      id: 'id',
      label: 'Test ID',
      render: (run: IRun) => (
        <Stack direction="row" alignItems="center">
          <Box className={styles.phoneIconWrapper}>
            <Phone />
          </Box>
          <Typography className={styles.testId}>{run.id}</Typography>
        </Stack>
      ),
    },
    {
      id: 'endReason',
      label: 'End Reason',
      render: (run: IRun) => (
        <Typography sx={{ color: '#23272E', fontSize: '16px', fontWeight: 400 }}>
          {run.endReason || 'NA'}
        </Typography>
      ),
    },
    {
      id: 'latency',
      label: 'Latency',
      sortable: true,
      render: (run: IRun) => (
        <Typography sx={{ color: '#475467', fontSize: '14px', fontWeight: 400 }}>
          {run.latency?.toFixed(2) || '0.00'} ms
        </Typography>
      ),
    },
    {
      id: 'status',
      label: 'Status',
      sortable: false,
      render: (run: IRun) => <StatusBadge status={run.status} />,
    },
    {
      id: 'score',
      label: 'Score',
      sortable: true,
      render: (run: IRun) => <ScoreProgress percentage={run.score || 0} />,
    },
    {
      id: 'duration',
      label: 'Duration',
      sortable: false,
      render: (run: IRun) => (
        <Typography sx={{ color: '#475467', fontSize: '14px', fontWeight: 400 }}>
          {run.duration || '00:00mm'}
        </Typography>
      ),
    },
    {
      id: 'actions',
      label: 'Actions',
      sortable: false,
      render: (run: IRun) => (
        <IconButton
          className={styles.actionButton}
          onClick={(e) => {
            e.stopPropagation();
            handleMenuClick(e, run);
          }}
        >
          <MoreVert />
        </IconButton>
      ),
    },
  ];

  // Fetch latency data
  const {
    data: latencyData,
    isLoading: isLatencyLoading,
    error: latencyError,
  } = useQuery<unknown[]>({
    queryKey: ["latencyData", currentAgentId],
    queryFn: async () => {
      if (!currentAgentId) throw new Error("No agent selected");
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL_EVALUATION;
      const response = await fetch(
        `${baseUrl}/agents/${currentAgentId}/results/latency`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            ...(user?.token ? { Authorization: `Bearer ${user.token}` } : {}),
          },
        }
      );
      if (!response.ok) {
        throw new Error(`Failed to fetch latency data: ${response.status}`);
      }
      return response.json();
    },
    enabled: Boolean(currentAgentId && user?.token),
  });

  // Fill all months for the current year with latency data (0 if missing)
  const currentYear = dayjs().year();
  const months = [
    "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
  ];
  const visibleMonths = new Set(["Jan", "Mar", "May", "Jul", "Sep", "Nov", "Dec"]);
  const filledLatencyData = months.map((month) => {
    const found = (latencyData as {month: string, year: number, total_latency_ms: number}[] | undefined)?.find(
      (item) => item.month === month && Number(item.year) === currentYear
    );
    return {
      month,
      year: currentYear,
      total_latency_ms: found ? found.total_latency_ms : 0,
    };
  });

  return (
    <Box className={styles.wrap}>
      {isResultsLoading ? (
        <Paper elevation={0} sx={{ background: '#fff', borderRadius: '20px', p: 0, boxShadow: '0px 1px 4px rgba(16, 24, 40, 0.06)' }}>
          <TableContainer className={styles.resultsTable}>
            <Table>
              <TableHead>
                <TableRow>
                  {columns.map((column) => (
                    <TableCell key={column.id} align={column.id === 'actions' ? 'right' : 'left'}>
                      {column.label}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                <TableSkeleton />
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      ) : !runs?.length ? (
        <ResultsEmptyState />
      ) : (
        <Stack spacing={3}>
          {/* Metrics and Latency in 2-column grid */}
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
              gap: 3,
              alignItems: 'stretch',
              borderRadius: '34px',
              mt: 2,
              mb: 2,
            }}
          >
            {/* Latency Chart */}
            {filledLatencyData && filledLatencyData.length > 0 && (
              <Box
                sx={{
                  borderRadius: '34px',
                  gridColumn:
                    (!topMetricsData || topMetricsData.length === 0)
                      ? '1 / -1'
                      : undefined,
                }}
              >
                <LatencyBarChart
                  isLoading={isLatencyLoading}
                  error={latencyError}
                  data={filledLatencyData}
                  visibleMonths={visibleMonths}
                />
              </Box>
            )}
            {/* Overall Metrics */}
            {topMetricsData && (topMetricsData as Metric[]).length > 0 && (
              <Box
                sx={{
                  mb: { xs: 2, md: 0 },
                  gridColumn:
                    (!filledLatencyData || filledLatencyData.length === 0)
                      ? '1 / -1'
                      : undefined,
                }}
              >
                <OverallMetrics metrics={topMetricsData as Metric[]} />
              </Box>
            )}
          </Box>

          <Box sx={{ mt: 2, mb: 2 }}>
            <Typography className={styles.pageTitle}>My Runs ({runs?.length || 0})</Typography>
          </Box>

          <Paper elevation={0} sx={{ background: '#fff', borderRadius: '20px', p: 0, boxShadow: '0px 1px 4px rgba(16, 24, 40, 0.06)' }}>
            <TableContainer className={styles.resultsTable}>
              <Table>
                <TableHead>
                  <TableRow>
                    {columns.map((column) => (
                      <TableCell key={column.id} align={column.id === 'actions' ? 'right' : 'left'}>
                        {column.label}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {runs?.map((run) => (
                    <TableRow key={run.id}>
                      {columns.map((column) => (
                        <TableCell key={`${run.id}-${column.id}`} align={column.id === 'actions' ? 'right' : 'left'}>
                          {column.render(run)}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Stack>
      )}

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          elevation: 2,
          sx: {
            mt: 1,
            minWidth: 180,
            borderRadius: '8px',
            boxShadow: '0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08)',
            '& .MuiMenuItem-root': {
              fontSize: '14px',
              color: '#344054',
              py: 1,
              px: 2,
              '&:hover': {
                backgroundColor: '#F9FAFB',
              },
            },
          },
        }}
      >
        <MenuItem
          onClick={() => {
            setIsRunDetailsModalOpen(selectedRunForMenu);
            handleMenuClose();
          }}
        >
          View Details
        </MenuItem>
        {/* <MenuItem onClick={handleMenuClose}>Download Recording</MenuItem>
        <MenuItem onClick={handleMenuClose}>Download Transcript</MenuItem> */}
      </Menu>

      {isRunDetailsModalOpen && (
        <RunDetailsModal
          run={isRunDetailsModalOpen}
          onCancel={() => setIsRunDetailsModalOpen(null)}
        />
      )}
    </Box>
  );
}
