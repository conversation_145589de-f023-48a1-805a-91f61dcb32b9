import { useQuery } from '@tanstack/react-query';
import { fetchDashboardStats } from '../lib/fetchers';
export function useDashboardStatsQuery(currentAgentId: string) {
  return useQuery({
    queryKey: ['dashboardStats', currentAgentId], // <--- currentAgentId is part of the key!
    queryFn: () => fetchDashboardStats(currentAgentId),
    enabled: !!currentAgentId, // Optional: only run if agentId is truthy
  });
}