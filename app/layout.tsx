"use client";

import { useEffect} from "react";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import Script from "next/script";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import useTokenRefresh from "@/hooks/useTokenRefresh";
import {  NextStepProvider } from "nextstepjs";
import { GeneralStoreProvider } from "@/providers/general-store-provider";
import { useIsHydrated } from "@/providers/general-store-provider";



// Create a wrapper component to handle hydration
function HydrationWrapper({ children }: { children: React.ReactNode }) {
  const isHydrated = useIsHydrated();

  // Show loading state while hydrating
  if (!isHydrated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return <>{children}</>;
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  useTokenRefresh();
  const analytics = useRudderStackAnalytics();
  useEffect(() => {
    if (analytics) {
      analytics.page();
    }
  }, [analytics]);


  return (
    <html lang="en">
      <Script id="bufferEvents">
        {`
            window.rudderanalytics = [];
            var methods = [
              'setDefaultInstanceKey',
              'load',
              'ready',
              'page',
              'track',
              'identify',
              'alias',
              'group',
              'reset',
              'setAnonymousId',
              'startSession',
              'endSession',
              'consent'
            ];
            for (var i = 0; i < methods.length; i++) {
              var method = methods[i];
              window.rudderanalytics[method] = (function (methodName) {
                return function () {
                  window.rudderanalytics.push([methodName].concat(Array.prototype.slice.call(arguments)));
                };
              })(method);
            }
        `}
      </Script>
      <body>
        <GeneralStoreProvider>
          <HydrationWrapper>
            <AntdRegistry>
                <NextStepProvider>
                  {children}
                </NextStepProvider>
            </AntdRegistry>
          </HydrationWrapper>
        </GeneralStoreProvider>
      </body>
    </html>
  );
}
