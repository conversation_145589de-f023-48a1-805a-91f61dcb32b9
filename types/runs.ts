export enum ERunTranscriptionStatus {
  SUCCESS = "Success",
  FAILED = "Failed",
}


export interface IRun {
  id: number
  scenarioName: string
  timestamp: string
  duration: string
  transcriptionStatus: string
  recordingUrl: string
  transcriptionUrl: string
  callId: string
  metrics?: Metrics
  transcription: Transcription
  endReason: string
  latency: number
  status: string
  score: number
}

export interface Metrics {
  "Average Response Time": number
  "Agent Repetition"?: number
  "Transfer to Operator"?: number
}

export interface Transcription {
  call_sid?: string
  stream_sid?: string
  stream_started?: string
  segments?: Segment[]
}

export interface Segment {
  item_id: string
  speaker: string
  timestamp_start: string
  timestamp_end: string
  add_to_history_time: string
  transcript?: string
  previous_item_id?: string
  response_id?: string
}



