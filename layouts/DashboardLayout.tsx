import { PropsWithChildren } from "react";
import { Box, useMediaQuery, useTheme } from "@mui/material";
import Navbar from "@/components/Navbar/Navbar";
import SideNav from "@/components/SideNav/SideNav";
import { SidebarProvider } from "@/providers/side-menu-context";
import { NotificationProvider } from "@/context/NotificationContext/NotificationContext";

const drawerWidth = 240;
const collapsedWidth = 80;

const DashboardLayout = ({ children }: PropsWithChildren) => {
  const muiTheme = useTheme();
  const isMobile = useMediaQuery(muiTheme.breakpoints.down("sm"));

  return (
    <NotificationProvider>
      <SidebarProvider>
        <Box sx={{ display: "flex", minHeight: "100vh" }}>
          <SideNav />

          <Box
            component="main"
            sx={{
              flexGrow: 1,
              width: isMobile ? "100%" : `calc(100% - ${drawerWidth}px)`,
              transition: "width 0.3s",
              paddingLeft: "1rem",
              paddingRight: "1rem",
              paddingBottom: "2rem",
            }}
          >
            <Box sx={{ margin: "0rem 2rem" }}>
              <Navbar logo={false} showNewSimulation={true} />
            </Box>

            <Box sx={{ pr: 4, pl: 4 }}>{children}</Box>
          </Box>
        </Box>
      </SidebarProvider>
    </NotificationProvider>
  );
};

export default DashboardLayout;
