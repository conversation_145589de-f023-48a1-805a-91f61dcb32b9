import React, { createContext, Props<PERSON><PERSON><PERSON><PERSON>dren, useContext } from "react";
import { ConfigProvider, notification } from "antd";

import { useMemoObject } from "@/hooks/useMemoObject";

import { notificationTheme } from "./constants";
import { NotificationInstance } from "antd/es/notification/interface";

const NotificationContext = createContext<NotificationInstance | null>(null);

export const useNotification = (): NotificationInstance => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error(
      "useNotification must be used within a NotificationProvider",
    );
  }
  return context;
};

export const NotificationProvider = ({ children }: PropsWithChildren) => {
  const [api, contextHolder] = notification.useNotification();

  const contextValue = useMemoObject({
    ...api,
  });

  return (
    <NotificationContext.Provider value={contextValue}>
      <ConfigProvider theme={notificationTheme}>
        {contextHolder}
        {children}
      </ConfigProvider>
    </NotificationContext.Provider>
  );
};
