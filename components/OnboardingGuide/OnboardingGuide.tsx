import React from 'react';
import RightBottomDialog from '../RightBottomDialog/RightBottomDialog';
import { useOnboardingStore } from '@/stores/onboarding-store';
import { Box, Typography, Button } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';

const OnboardingGuide = () => {
  const {
    hasCompletedOnboarding,
    currentStep,
    steps,
    completeOnboarding
  } = useOnboardingStore();

  if (hasCompletedOnboarding) {
    return null;
  }

  const steps_content = [
    {
      title: "Create Your First Agent",
      description: "Start by creating your first AI agent to handle specific tasks.",
      completed: steps.createAgent
    },
    {
      title: "Create AI Evaluator",
      description: "Generate scenarios or create an AI evaluator to test your agent.",
      completed: steps.createScenario
    },
    {
      title: "Run Your First Test",
      description: "Execute your first test to see your agent in action.",
      completed: steps.runScenario
    }
  ];

  const isLastStep = currentStep === 4;

  return (
    <RightBottomDialog
      open={true}
      title={isLastStep ? "Congratulations! 🎉" : "Getting Started Guide"}
      onClose={() => {}}
      hideCloseButton={true}
    >
      {!isLastStep ? (
        <Box sx={{ mb: 3 }}>
          {steps_content.map((step, index) => (
            <Box
              key={index}
              sx={{
                display: 'flex',
                alignItems: 'flex-start',
                mb: 2,
                opacity: currentStep === index + 1 ? 1 : 0.5
              }}
            >
              {step.completed ? (
                <CheckCircleIcon sx={{ color: '#12B76A', mr: 1, mt: 0.3 }} />
              ) : (
                <RadioButtonUncheckedIcon sx={{ color: '#98A2B3', mr: 1, mt: 0.3 }} />
              )}
              <Box>
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontWeight: 600,
                    color: '#101828',
                    fontFamily: 'Plus Jakarta Sans',
                    fontSize: '14px',
                    mb: 0.5
                  }}
                >
                  {step.title}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: '#475467',
                    fontFamily: 'Plus Jakarta Sans',
                    fontSize: '14px'
                  }}
                >
                  {step.description}
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>
      ) : (
        <Box sx={{ textAlign: 'center', py: 2 }}>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: '#101828',
              fontFamily: 'Plus Jakarta Sans',
              mb: 2
            }}
          >
            You&apos;ve completed all the steps!
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: '#475467',
              fontFamily: 'Plus Jakarta Sans',
              mb: 3
            }}
          >
            You&apos;re now ready to use all features of the platform.
          </Typography>
          <Button
            variant="contained"
            onClick={completeOnboarding}
            sx={{
              backgroundColor: '#7F56D9',
              color: '#fff',
              textTransform: 'none',
              fontFamily: 'Plus Jakarta Sans',
              '&:hover': {
                backgroundColor: '#6941C6'
              }
            }}
          >
            Run Again
          </Button>
        </Box>
      )}
    </RightBottomDialog>
  );
};

export default OnboardingGuide; 