import React from 'react';
import { Box, Dialog, DialogContent, Typography } from '@mui/material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

export type StatusType = 'success' | 'error';

interface StatusDialogProps {
  open: boolean;
  onClose: () => void;
  status: StatusType;
  title?: string;
  message?: string;
  autoCloseDelay?: number;
  onAutoClose?: () => void;
}

const StatusDialog: React.FC<StatusDialogProps> = ({
  open,
  onClose,
  status,
  title,
  message,
  autoCloseDelay,
  onAutoClose,
}) => {
  React.useEffect(() => {
    if (open && autoCloseDelay && onAutoClose) {
      const timer = setTimeout(() => {
        onAutoClose();
      }, autoCloseDelay);
      return () => clearTimeout(timer);
    }
  }, [open, autoCloseDelay, onAutoClose]);

  const defaultTitles = {
    success: "Congratulations! 🎉",
    error: "Oops! Something went wrong"
  };

  const defaultMessages = {
    success: "Your action has been completed successfully!",
    error: "Please try again or contact support if the problem persists."
  };

  const iconColors = {
    success: '#00C853',
    error: '#F04438'
  };

  const IconComponent = status === 'success' ? CheckCircleOutlineIcon : ErrorOutlineIcon;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '16px',
          padding: '24px',
          textAlign: 'center',
        }
      }}
    >
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
          <IconComponent 
            sx={{ 
              fontSize: 64, 
              color: iconColors[status],
              mb: 2
            }} 
          />
          <Typography variant="h4" sx={{ 
            fontWeight: 600, 
            color: '#101828',
            fontFamily: 'Plus Jakarta Sans'
          }}>
            {title || defaultTitles[status]}
          </Typography>
          <Typography variant="body1" sx={{ 
            color: '#667085',
            fontFamily: 'Plus Jakarta Sans',
            maxWidth: '400px'
          }}>
            {message || defaultMessages[status]}
          </Typography>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default StatusDialog; 