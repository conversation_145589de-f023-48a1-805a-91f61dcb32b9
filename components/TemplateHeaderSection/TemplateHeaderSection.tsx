import React from 'react';
import { Box, Typography } from '@mui/material';
import Image from 'next/image';

interface TemplateHeaderSectionProps {
    icon: string;
    title: string;
    rightContent?: React.ReactNode;
    button?: React.ReactNode;
}

const TemplateHeaderSection: React.FC<TemplateHeaderSectionProps> = ({
    icon,
    title,
    rightContent,
    button,
}) => {
    return (
        <Box
            sx={{
                minHeight: 56,
                padding: '0px 20px',
                background: '#ffffff',
                border: '1px solid #EDEDED',
                mb: 3,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderRadius: '20px',
            }}
        >
            <Box display="flex" alignItems="center" gap={2}>
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}
                >
                    <Image src={icon} alt="Header Icon" width={16.5} height={16.5} />
                </Box>
                <Typography
                    variant="h6"
                    sx={{
                        fontFamily: 'Plus Jakarta Sans',
                        fontSize: 16,
                        fontWeight: 600,
                    }}
                >
                    {title}
                </Typography>
            </Box>
            {(rightContent || button) && (
                <Box display="flex" alignItems="center" gap={1}>
                    {rightContent}
                    {button}
                </Box>
            )}
        </Box>
    );
};

export default TemplateHeaderSection;
