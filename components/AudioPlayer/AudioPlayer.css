.custom-audio-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 20px 8px 8px;
  background: #F4F6F8;
  border-radius: 20px;
  border: 1px solid #ECEDEF;
  width: 100%;
  position: relative;
  & > .rhap_container {
      background-color: transparent;
     box-shadow: none;
  }
}

.header {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 0px 12px;
}

.audio-text {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16px;
}

.audio-text-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.audio-title {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 600;
  font-size: 12px;
  color: #191A1D;
}

.audio-subtitle {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #8C9098;
}

.sound-wave-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-h5-player {
  box-shadow: none;
  background: transparent;
}

/* Progress Bar Styling */
.rhap_progress-bar {
  background-color: #E7E9EF;
  border-radius: 6px;
  height: 6px;
}

.rhap_progress-indicator {
  background-color: #7F56D9;
  border-radius: 50%;
}

.rhap_progress-filled {
  background-color: #7F56D9;
  border-radius: 6px;
}

/* Time Display Styling */
.rhap_time {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #8C9098;
  margin: 0;
  padding: 0;
}

/* Controls Styling */
.rhap_main-controls {
  display: flex;
  gap: 28px;
}

.rhap_button-clear {
  color: #191A1D;
  background: none;
  border: none;
  font-family: 'SF Pro Text', sans-serif;
  font-weight: 600;
}

/* Custom time display to show remaining time with minus sign */
.rhap_time.rhap_duration::before {
  content: '-';
}