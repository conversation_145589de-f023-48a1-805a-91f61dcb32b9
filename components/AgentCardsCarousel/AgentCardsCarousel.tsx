"use client";

import React from "react";
import {
  <PERSON>,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  Tooltip,
} from "@mui/material";
import Image from "next/image";
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import EditIcon from '@mui/icons-material/Edit';
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import DeleteIcon from '@mui/icons-material/Delete';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import styles from "./AgentCardsCarousel.module.scss";
import { useGeneralStore } from "@/providers/general-store-provider";
import type { ComponentType, ReactNode } from 'react';
import type { Settings } from 'react-slick';
import CreateEntityCard from "@/components/CreateEntityCard/CreateEntityCard";

export interface AgentCardData {
  id: string;
  name: string;
  description: string;
  metricsCount: number;
  metrics: string[];
  moreCount: number;
  currentAgentId?: string;
  isCurrentAgent?: boolean;
  onSelect?: () => void;
  onDelete?: (id: string) => void;
}

interface ArrowProps {
  onClick?: () => void;
  currentSlide?: number;
}

function CustomPrevArrow({ onClick, currentSlide }: ArrowProps) {
  if (currentSlide === 0) return null;
  return (
    <IconButton
      onClick={onClick}
      className={styles.prevArrow}
    >
      <ArrowBackIcon sx={{ fontSize: 20 }} />
    </IconButton>
  );
}

function CustomNextArrow({ onClick }: ArrowProps) {
  return (
    <IconButton
      onClick={onClick}
      className={styles.nextArrow}
    >
      <ArrowForwardIcon sx={{ fontSize: 20 }} />
    </IconButton>
  );
}

function AgentCard({ data }: { data: AgentCardData }) {
  const { setCurrentAgentId } = useGeneralStore((state) => state);
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [isHovered, setIsHovered] = React.useState(false);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEditClick = () => {
    if (data.onSelect) {
      data.onSelect();
    }
  };

  const handleSwitchAgent = () => {
    setCurrentAgentId(data.id);
  };

  const handleDeleteAgent = () => {
    debugger;
    if (data.onDelete) {
      data.onDelete(data.id);
    }
  };

  return (
    <Box
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      sx={{
        height: '100%',
        minHeight: '230px',
        borderRadius: '24px',
        p: 3,
        display: "flex",
        backgroundColor: '#FFFFFF',
        flexDirection: "column",
        cursor: "default",
        transition: 'all 0.2s ease-in-out',
        position: 'relative',
        border: data.isCurrentAgent ? '2px solid #7F56D9' : '1px solid #E3E3E3',
        '&:hover': {
          border: data.isCurrentAgent ? '2px solid #7F56D9' : '1px solid #7F56D9',
        }
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2.5 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Image src="/audio-icon.svg" alt="Agent Icon" width={52} height={52} />
        </Box>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
        }}>
          {data.metricsCount > 0 && (<Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            backgroundColor: "#F9F5FF",
            borderRadius: "16px",
            padding: "4px 12px",
          }}>
            <Image src="/metrics-vector.svg" alt="Metrics Icon" width={16} height={16} />

            <Typography
              sx={{
                color: "#7F56D9",
                fontWeight: 600,
                fontFamily: "Plus Jakarta Sans",
                fontSize: "14px",
              }}
            >
              {data.metricsCount} Metrics
            </Typography>
          </Box>)}

          {data.isCurrentAgent && (
            <Box
              sx={{
                backgroundColor: '#7F56D9',
                color: 'white',
                borderRadius: "16px",
                padding: "4px 12px",
                fontSize: '14px',
                fontWeight: 600,
                fontFamily: 'Plus Jakarta Sans',
                ml: 1,
              }}
            >
              Current
            </Box>
          )}
        </Box>
      </Box>

      <Box sx={{ mb: 2.5 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              fontSize: "20px",
              lineHeight: "30px",
              color: "#101828",
              fontFamily: "Plus Jakarta Sans"
            }}
          >
            {data.name.length > 25 ? (
              <Tooltip title={data.name}
                placement="top"
                slotProps={{
                  tooltip: {
                    sx: {
                      background: 'rgba(255,255,255,0.1)',
                      backdropFilter: 'blur(10px)',
                      WebkitBackdropFilter: 'blur(10px)',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
                      fontFamily: 'Plus Jakarta Sans',
                      color: '#000',
                      minWidth: 200,
                      padding: '16px 20px',
                      fontSize: '14px',
                      borderRadius: '8px',
                    }
                  }
                }}  >
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 600,
                    fontSize: "20px",
                    lineHeight: "30px",
                    color: "#101828",
                    fontFamily: "Plus Jakarta Sans",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    cursor: "pointer"
                  }}
                >
                  {Array.from(data.name).slice(0, 25).join('') + (data.name.length > 25 ? '…' : '')}
                </Typography>
              </Tooltip>
            ) : (
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  fontSize: "20px",
                  lineHeight: "30px",
                  color: "#101828",
                  fontFamily: "Plus Jakarta Sans",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap"
                }}
              >
                {data.name}
              </Typography>
            )}
          </Typography>
          <>
            <IconButton
              aria-label="more"
              id={`long-button-${data.id}`}
              aria-controls={Boolean(anchorEl) ? `long-menu-${data.id}` : undefined}
              aria-expanded={Boolean(anchorEl) ? 'true' : undefined}
              aria-haspopup="true"
              onClick={handleMenuClick}
              sx={{
                color: '#667085',
                visibility: isHovered ? 'visible' : 'hidden',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                }
              }}
            >
              <MoreHorizIcon />
            </IconButton>
            <Menu
              id={`long-menu-${data.id}`}
              MenuListProps={{
                'aria-labelledby': `long-button-${data.id}`,
              }}
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
              sx={{
                fontFamily: "Plus Jakarta Sans",
              }}
              slotProps={{
                paper: {
                  style: {
                    maxHeight: 48 * 4.5,
                    width: '12ch',
                    borderRadius: '14px',
                    marginTop: 8,
                  },
                },
              }}
            >
              <MenuItem
                onClick={() => { handleEditClick(); handleMenuClose(); }}
                sx={{ fontFamily: "Plus Jakarta Sans", fontWeight: 600 }}
              >
                <ListItemIcon>
                  <EditIcon fontSize="small" />
                </ListItemIcon>
                Edit
              </MenuItem>
              {!data.isCurrentAgent && (
                <>
                  <MenuItem
                    onClick={() => { handleSwitchAgent(); handleMenuClose(); }}
                    sx={{ fontFamily: "Plus Jakarta Sans", fontWeight: 600 }}
                  >
                    <ListItemIcon>
                      <SwapHorizIcon fontSize="small" />
                    </ListItemIcon>
                    Switch
                  </MenuItem>
                </>
              )}
              <MenuItem
                onClick={() => { handleDeleteAgent(); handleMenuClose(); }}
                sx={{ color: 'error.main', fontFamily: "Plus Jakarta Sans", fontWeight: 600 }}
              >
                <ListItemIcon>
                  <DeleteIcon fontSize="small" sx={{ color: 'error.main' }} />
                </ListItemIcon>
                Delete
              </MenuItem>
            </Menu>
          </>
        </Box>
        <Typography
          variant="body2"
          sx={{
            color: "#475467",
            fontSize: "14px",
            lineHeight: "20px",
            fontFamily: "Plus Jakarta Sans",
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {data.description}
        </Typography>
      </Box>
    </Box>
  );
}

interface AgentCardsCarouselProps {
  items: AgentCardData[];
  onCreateAgent: () => void;
}

// Define a proper type for the Slider component props
interface SliderProps extends Settings {
  children: ReactNode;
}

// Create a properly typed Slider component
const TypedSlider: ComponentType<SliderProps> = Slider as unknown as ComponentType<SliderProps>;

export default function AgentCardsCarousel({ items, onCreateAgent }: AgentCardsCarouselProps) {
  const { currentAgentId } = useGeneralStore((state) => state);

  const settings: Settings = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    swipeToSlide: true,
    arrows: true,
    variableWidth: false,

    prevArrow: <CustomPrevArrow />,
    nextArrow: <CustomNextArrow />,
    initialSlide: 0,
    centerMode: false,
    centerPadding: '0px',
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1.5,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  const orderedItems = React.useMemo(() => {
    if (!items.length || !currentAgentId) return items;

    const currentAgentIndex = items.findIndex(item => item.id === currentAgentId);
    if (currentAgentIndex === -1) return items;

    const reorderedItems = [...items];
    const [currentAgent] = reorderedItems.splice(currentAgentIndex, 1);
    reorderedItems.unshift(currentAgent);

    return reorderedItems;
  }, [items, currentAgentId]);

  return (
    <Box sx={{
      width: "100%",
      position: "relative",
      '& .slick-prev': {
        left: '-40px',
        top: '50%',
        transform: 'translateY(-50%)',
      },
      '& .slick-next': {
        right: '-40px',
        top: '50%',
        transform: 'translateY(-50%)',
      },
      '& .slick-arrow': {
        width: '32px',
        height: '32px',
        borderRadius: '50%',
        backgroundColor: 'white',
        '&:hover': {
          backgroundColor: '#F9F5FF',
        },
        '&:before': {
          display: 'none',
        },
      },
      '& .slick-list': {
        margin: 0,
        padding: 0,
      },
      '& .slick-track': {
        display: 'flex',
        margin: 0,
        marginLeft: 0,
        marginRight: 'auto',
      },
      '& .slick-slide': {
        padding: '0 12px',
      },
      '& .slick-slide:first-of-type': {
        paddingLeft: 0,
      },
    }}>
      <TypedSlider {...settings}>
        <Box sx={{ outline: "none", mr: 2, minHeight: '230px', height: '100%' }}>
          <CreateEntityCard
            title="Create Agent"
            description="Create a new agent to test your scenarios"
            onClick={onCreateAgent}
            sx={{ minHeight: '230px', height: '100%' }}
          />
        </Box>
        {orderedItems.map((item) => (
          <Box key={item.id} sx={{ outline: "none", mr: 2 }}>
            <AgentCard data={item} />
          </Box>
        ))}
      </TypedSlider>
    </Box>
  );
}
