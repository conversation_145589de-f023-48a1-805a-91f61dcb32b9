@import "./../../styles/variables.module.scss";

.createAgentCard {
    height: 100%;
    min-height: 230px;
    border-radius: 24px !important;
    padding: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: #6941C6 !important;
    background-image: url('/Group.svg') !important;
    background-size: 40% !important;
    background-repeat: no-repeat !important;    
    background-position: right !important;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    position: relative;
    overflow: hidden;

    &:hover {
        background-color: #7F56D9;
    }
}

.createAgentButton {
    background-color: rgba(255, 255, 255, 0.1) !important;
    width: 48px !important;
    height: 48px !important;
    margin-bottom: 16px !important;
    transition: all 0.2s ease-in-out;

    &:hover {
        background-color: rgba(255, 255, 255, 0.2) !important;
    }
}

.addIcon {
    color: white;
    font-size: 24px;
}

.nextArrow{
    position: absolute !important;
    right: 8px;
    top: 50% !important; 
    transform: translateY(-50%) !important;
    z-index: 2;
    color: $white !important;
    background-color: $primary-light !important;
    &:hover {
        background-color: #f0f0f0;
    }
}

.prevArrow {
    position: absolute !important;
    left: 8px;
    top: 50%;
    transform: translateY(-50%) !important;
    z-index: 2;
    background-color: $primary-light !important;
    color: $white !important;
    &:hover {
        background-color: #f0f0f0;
    }
}