"use client";
import { Box, Typography } from '@mui/material';
import { useState, useEffect } from 'react';
import { useAuthStore } from '@/stores/auth-store';

const getGreeting = () => {
  const hour = new Date().getHours();

  if (hour < 12) {
    return 'Good morning';
  } else if (hour < 18) {
    return 'Good afternoon';
  } else {
    return 'Good evening';
  }
};

interface DashboardHeaderProps {
  activeRuns: number;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({ activeRuns }) => {
  const { user } = useAuthStore((state) => state);
  const [greeting, setGreeting] = useState<string>(getGreeting());

  // Update greeting every minute to handle time changes
  useEffect(() => {
    const interval = setInterval(() => {
      setGreeting(getGreeting());
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  const userName = user ? `${user.first_name || ''} ${user.last_name || ''}`.trim() || 'User' : 'User';

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
        flexDirection: 'column',
        gap: 2
      }}
    >
      {/* Active Runs Counter */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          backgroundColor: '#fff',
          borderRadius: '48px',
          border: '1px solid #E5E6E6',
          px: 2,
          minHeight: 38,
          minWidth: 150,
          boxSizing: 'border-box',
        }}
      >
        <Box
          sx={{
            position: 'relative',
            width: 15,
            height: 15,
            minWidth: 15,
            minHeight: 15,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '50%',
            backgroundColor: '#E5E6E6',
            mr: 1,
          }}
        >
          <Box
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: 'gray',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Box
              sx={{
                width: 4,
                height: 4,
                borderRadius: '50%',
                backgroundColor: '#232B29',
              }}
            />
          </Box>
        </Box>
        <Typography
          component="span"
          sx={{
            fontSize: '14px',
            fontWeight: 600,
            fontFamily: 'Plus Jakarta Sans',
            color: '#232B29',
            letterSpacing: 0,
            lineHeight: 1,
            mr: 1,
          }}
        >
          Active Runs:
        </Typography>
        <Typography
          component="span"
          sx={{
            fontSize: '14px',
            fontWeight: 600,
            fontFamily: 'Plus Jakarta Sans',
            color: '#232B29',
            letterSpacing: 0,
            lineHeight: 1,
          }}
        >
          {activeRuns}
        </Typography>
      </Box>

      {/* Header Content */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: '100%'
        }}
      >
        {/* Left: Greeting and Subtext */}
        <Box>
          <Typography sx={{
            fontSize: '16px',
            fontWeight: 500,
            color: '#707171',
            lineHeight: '24px',
            fontFamily: "Plus Jakarta Sans"
          }} >
            My workspace
          </Typography>
          <Typography sx={{
            fontSize: '24px',
            fontWeight: 700,
            fontFamily: "Plus Jakarta Sans",
            lineHeight: '40px'
          }} >
            {greeting}, {userName}
          </Typography>
          {/* Optional Subheader or any context can go here */}
        </Box>

        {/* Right: Filters and Avatar */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* Filter 1: Agents */}
          {/* <FormControl variant="outlined" size="small">
            <Select
              value={agentFilter}
              onChange={(e) => setAgentFilter(e.target.value as string)}
              sx={{ 
                minWidth: 160,
                height: 48,
                backgroundColor: 'white',
                borderRadius: '100px',
                boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)',
                '.MuiSelect-select': {
                  fontFamily: 'Plus Jakarta Sans',
                  fontWeight: 600,
                  fontSize: '0.95rem',
                  color: '#1E293B',
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'transparent',
                  borderRadius: '100px'
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'transparent',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'transparent',
                  boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.08)',
                }
              }}
              MenuProps={{
                PaperProps: {
                  sx: {
                    borderRadius: 3,
                    mt: 1,
                    boxShadow: '0px 4px 16px rgba(0, 0, 0, 0.08)',
                  },
                },
              }}
            >
              <MenuItem value="all" sx={{ fontFamily: 'Plus Jakarta Sans' }}>All Agents</MenuItem>
              <MenuItem value="agent1" sx={{ fontFamily: 'Plus Jakarta Sans' }}>Agent 1</MenuItem>
              <MenuItem value="agent2" sx={{ fontFamily: 'Plus Jakarta Sans' }}>Agent 2</MenuItem>
            </Select>
          </FormControl> */}

          {/* Filter 2: Time Range */}
          {/* <FormControl variant="outlined" size="small">
            <Select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value as string)}
              sx={{ 
                minWidth: 160,
                height: 48,
                backgroundColor: 'white',
                borderRadius: '100px',
                boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)',
                '.MuiSelect-select': {
                  fontFamily: 'Plus Jakarta Sans',
                  fontWeight: 600,
                  fontSize: '0.95rem',
                  color: '#1E293B',
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'transparent',
                  borderRadius: '100px'
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'transparent',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'transparent',
                  boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.08)',
                }
              }}
              MenuProps={{
                PaperProps: {
                  sx: {
                    borderRadius: 3,
                    mt: 1,
                    boxShadow: '0px 4px 16px rgba(0, 0, 0, 0.08)',
                  },
                },
              }}
            >
              <MenuItem value="lastMonth" sx={{ fontFamily: 'Plus Jakarta Sans' }}>Last Month</MenuItem>
              <MenuItem value="lastWeek" sx={{ fontFamily: 'Plus Jakarta Sans' }}>Last Week</MenuItem>
              <MenuItem value="lastYear" sx={{ fontFamily: 'Plus Jakarta Sans' }}>Last Year</MenuItem>
            </Select>
          </FormControl> */}
        </Box>
      </Box>
    </Box>
  );
};

export default DashboardHeader;
