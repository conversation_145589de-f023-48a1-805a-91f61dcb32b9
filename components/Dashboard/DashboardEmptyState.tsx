import { Box, Typography } from "@mui/material";
import Image from "next/image";

export default function DashboardEmptyState() {
    return (
        <Box
            sx={{
                width: "100%",
                height: 500,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                bgcolor: "#fff",
                borderRadius: 4,
                border: "1px solid #F0F0F0",
            }}
        >
            <Box sx={{
                display: "flex",
                height: "56px",
                width: "56px",
                alignItems: "center",
                justifyContent: "center",
                bgcolor: "#F8FAFC",
                padding: '12px',
                marginBottom: '16px',
                borderRadius: 2,
            }}>
                <Image src="/empty-dashboard-chart.svg" alt="Empty State" width={32} height={32} />
            </Box>
            <Typography variant="h6" fontWeight={600} mb={1} sx={{ fontFamily: 'Plus Jakarta Sans' }}>
                No data found
            </Typography>
            <Typography color="text.secondary" sx={{ fontFamily: 'Plus Jakarta Sans' }}>
                There is no data for the specified period.
            </Typography>
        </Box>
    );
}