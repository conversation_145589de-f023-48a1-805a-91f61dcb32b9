// components/Dashboard/DashboardKpiCards.tsx
"use client";
import React from 'react';
import { Box, CardContent, Typography } from '@mui/material';

interface DashboardKpiCardsProps {
  totalRuns: number;
  averageDuration: string;
  failureRate: string;
}

const DashboardKpiCards: React.FC<DashboardKpiCardsProps> = ({
  totalRuns,
  averageDuration,
  failureRate,
}) => {
  const kpiData = [
    {
      title: 'Number of Runs',
      value: totalRuns.toLocaleString(),
    },
    {
      title: 'Average Duration',
      value: averageDuration,
    },
    {
      title: 'Failure Rate',
      value: failureRate,
    },
  ];

  return (
    <Box 
      sx={{ 
        display: 'flex', 
        gap: 2,
        flexWrap: 'wrap'
      }}
    >
      {kpiData.map((item, idx) => (
        <Box 
          key={idx} 
          sx={{ 
            minWidth: 200, 
            flex: '1 1 auto', 
            border: '1px solid #E5E6E6',
            backgroundColor: '#ffffff',
            borderRadius: '20px',
          }}
        >
          <CardContent>
            <Typography  sx={{
              fontSize: '14px',
              fontWeight: 500,
              fontFamily: "Plus Jakarta Sans",
              color: '#242E2C',
              lineHeight: '20px',
              marginBottom: '8px',
            }}>
              {item.title}
            </Typography>
            <Typography sx={{
              fontSize: '26px',
              fontWeight: 700,
              fontFamily: "Plus Jakarta Sans",
              color: '#232B29',
              lineHeight: '32px',
            }}>
              {item.value}
            </Typography>
          </CardContent>
        </Box>
      ))}
    </Box>
  );
};

export default DashboardKpiCards;
