"use client";
import React, { useState, useMemo } from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  Avatar,
  IconButton,
  Box,
  useMediaQuery,
  Skeleton,
  Button,
} from '@mui/material';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import MenuIcon from '@mui/icons-material/Menu';
import { useTheme } from '@mui/material/styles';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/auth-store';
import LogoBeta from '../Logo-beta/Logo-beta';
// import CreateAIEvaluatorModal from '@/app/(dashboard)/simulation/evaluator/components/CreateAIEvaluatorModal/CreateAIEvaluatorModal';
import Routes from '@/constants/routes';
import Paper from '@mui/material/Paper';
import Divider from '@mui/material/Divider';
import LogoutIcon from '@mui/icons-material/Logout';
import CircularProgress from '@mui/material/CircularProgress';
import ClickAwayListener from '@mui/material/ClickAwayListener';

const useSidebar = () => ({
  toggleSidebar: () => console.log('Toggle sidebar'),
});


interface NavbarProps {
  logo: boolean;
  showNewSimulation?: boolean;
}

const Navbar = ({ logo = false }: NavbarProps) => {
  const { logout, user } = useAuthStore((state) => state);
  // const [, setCreateAIEvaluatorOpen] = useState(false);

  const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);

  const router = useRouter();
  const theme = useTheme();
  const isMediumScreen = useMediaQuery(theme.breakpoints.up('md'));
  const { toggleSidebar } = useSidebar();
  const userAvatar = useMemo(() => {
    return [user?.email || '']
      .map((word) => word.charAt(0).toUpperCase())
      .join('');
  }, [user]);

  const handleProfileMenuToggle = (event: React.MouseEvent<HTMLDivElement>) => {
    setAnchorEl(anchorEl ? null : event.currentTarget as HTMLDivElement);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const logoutUser = () => {
    logout();
    handleProfileMenuClose();
    router.push('/login');
  };

  // const handleCreateAIEvaluator = () => {
  //   setCreateAIEvaluatorOpen(true);
  // };

  // Mock values for credits
  const totalCredits = user?.subscription?.plan_snapshot.features.credits || 0;
  const remainingCredits = (user?.subscription?.plan_snapshot.features.credits - user?.subscription?.remaining_credits)? 0 :  user?.subscription?.plan_snapshot.features.credits;
  const creditsPercent = (remainingCredits / totalCredits) * 100;

  return (
    <>
      <AppBar
        position="relative"
        sx={{
          backgroundColor: '#fff',
          boxShadow: 'none',
          color: '#000',
          borderRadius: 4,
          margin: '1rem 0rem',
          justifyContent: 'center',
          height: "75px",
          zIndex: 999,
          width: '100%',
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between', padding: '0 16px' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            {logo && <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
              <LogoBeta />
            </Box>
            }
            {!isMediumScreen && (
              <IconButton onClick={toggleSidebar}>
                <MenuIcon sx={{ color: '#000' }} />
              </IconButton>
            )}

            {/* {showNewSimulation && (
              <Button
                variant="text"
                onClick={handleCreateAIEvaluator}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  backgroundColor: '#FFFFFF',
                  border: '1px solid #E3E3E3',
                  borderRadius: '14px',
                  padding: '12px 20px',
                  color: '#000',
                  textTransform: 'none',
                  '&:hover': {
                    backgroundColor: '#FFFFFF',
                    boxShadow: '0px 2px 4px rgba(16, 24, 40, 0.1), 0px 1px 3px rgba(16, 24, 40, 0.06)',
                  },
                  height: '44px',
                  minWidth: '200px',
                  fontFamily: 'Plus Jakarta Sans',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <Image src="/atomic-power.svg" alt="Simulation" width={20} height={20} />
                    <Typography
                      sx={{
                        fontSize: '14px',
                        fontWeight: 600,
                        lineHeight: '20px',
                        color: '#101828',
                        fontFamily: 'Plus Jakarta Sans',
                      }}
                    >
                      New Simulation
                    </Typography>
                  </Box>
                  <AddIcon sx={{ fontSize: 20, color: '#98A2B3' }} />
                </Box>
              </Button>
            )} */}
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>

            {/* Notification Bell with Red Dot */}
            {/* <IconButton>
              <Badge
                overlap="circular"
                anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                badgeContent={
                  <Box
                    sx={{
                      width: '10px',
                      height: '10px',
                      backgroundColor: '#ff0000',
                      borderRadius: '50%',
                      border: '2px solid #fff',
                    }}
                  />
                }
              >
                <NotificationsIcon sx={{ color: '#000' }} />
              </Badge>
            </IconButton> */}

            {/* User Info with Dropdown */}
            {user ? (
              <>
                <Avatar
                  sx={{
                    width: '40px',
                    height: '40px',
                    backgroundColor: '#4caf50',
                  }}
                >
                  {userAvatar}
                </Avatar>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: '12px',
                      color: '#A3A3A3',
                      fontFamily: 'Plus Jakarta Sans'
                    }}
                  >
                    Welcome
                  </Typography>
                  <Box
                    sx={{ display: 'flex', alignItems: 'center', gap: '4px', cursor: 'pointer' }}
                    onClick={handleProfileMenuToggle}
                    tabIndex={0}
                    onKeyDown={(e: React.KeyboardEvent) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        handleProfileMenuToggle(e as unknown as React.MouseEvent<HTMLDivElement>);
                      }
                    }}
                    aria-haspopup="true"
                    aria-expanded={!!anchorEl}
                    role="button"
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 'bold',
                        fontSize: '14px',
                        color: '#000',
                        fontFamily: 'Plus Jakarta Sans'
                      }}
                    >
                      {user.email}
                    </Typography>
                    <ArrowDropDownIcon sx={{ color: '#000' }} />
                  </Box>
                </Box>
                {/* Custom Profile Menu with ClickAwayListener */}
                {Boolean(anchorEl) && (
                  <ClickAwayListener onClickAway={handleProfileMenuClose} >
                    <Paper
                      elevation={8}
                      sx={{
                        position: 'absolute',
                        top: '60px',
                        right: '24px',
                        borderRadius: '20px',
                        minWidth: 240,
                        background: '#fff',
                        boxShadow: '0px 4px 24px rgba(0,0,0,0.10)',
                        zIndex: 2000,
                      }}
                    >
                      {/* Credits Section */}
                      {user.subscription && (
                        <>
                          <Box sx={{ display: 'flex', flexDirection: 'column', background: '#F5F5F5', p: 2, overflow: 'hidden', borderTopLeftRadius: 20, borderTopRightRadius: 20 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', mb: 1.5 }}>
                              <Box sx={{ position: 'relative', width: 36, height: 36, mr: 1.5 }}>
                                {/* Background circle */}
                                <CircularProgress
                                  variant="determinate"
                                  value={100}
                                  size={36}
                                  thickness={4}
                                  sx={{
                                    color: '#F2F2F2',
                                    position: 'absolute',
                                  }}
                                />
                                {/* Progress circle */}
                                <CircularProgress
                                  variant="determinate"
                                  value={creditsPercent}
                                  size={36}
                                  thickness={4}
                                  sx={{
                                    color: '#A689FA',
                                    position: 'absolute',
                                  }}
                                />
                                {/* Center white dot */}
                                <Box
                                  sx={{
                                    position: 'absolute',
                                    top: '50%',
                                    left: '50%',
                                    transform: 'translate(-50%, -50%)',
                                    width: 20,
                                    height: 20,
                                    background: '#F5F5F5',
                                    borderRadius: '50%',
                                    boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.1)',
                                  }}
                                />
                              </Box>
                              <Typography sx={{ fontWeight: 600, fontSize: 12, color: '#222', fontFamily: 'Plus Jakarta Sans' }}>Credits</Typography>
                              <Button
                                variant="contained"
                                sx={{
                                  ml: 'auto',
                                  background: '#915EFF',
                                  color: '#fff',
                                  borderRadius: '10px',
                                  fontWeight: 600,
                                  fontSize: 14,
                                  px: 2,
                                  py: 0.2,
                                  minWidth: 0,
                                  minHeight: 0,
                                  height: 28,
                                  boxShadow: 'none',
                                  textTransform: 'none',
                                  fontFamily: 'Plus Jakarta Sans',
                                  '&:hover': { background: '#8F6FE8' },
                                }}
                                onClick={() => router.push(Routes.pricing)}
                              >
                                Upgrade
                              </Button>
                            </Box>
                            <Box sx={{ display: 'flex', flexDirection: 'column', mb: 1.5, }}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.2 }}>
                                <Typography sx={{ color: '#76767F', fontSize: 12, fontWeight: 500, fontFamily: 'Plus Jakarta Sans' }}>Total</Typography>
                                <Typography sx={{ color: '#111', fontWeight: 600, fontSize: 12, fontFamily: 'Plus Jakarta Sans' }}>{totalCredits.toLocaleString()}</Typography>
                              </Box>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                <Typography sx={{ color: '#76767F', fontSize: 12, fontWeight: 500, fontFamily: 'Plus Jakarta Sans' }}>Remaining</Typography>
                                <Typography sx={{ color: '#111', fontWeight: 600, fontSize: 12, fontFamily: 'Plus Jakarta Sans' }}>{remainingCredits.toLocaleString()}</Typography>
                              </Box>
                            </Box>
                          </Box>
                          <Divider sx={{ border: '2px solid #EEEEEE' }} />
                          {/* <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1.2,
                              py: 0.7,
                              padding: '16px 16px',
                              cursor: 'pointer',
                              transition: 'background 0.2s',
                              '&:hover': { background: '#F5F5F5' },
                            }}
                            onClick={() => router.push(Routes.pricing)}
                          >
                            <CreditCardIcon sx={{ fontSize: 20, color: '#111' }} />
                            <Typography sx={{ fontWeight: 500, fontSize: 15, color: '#111', fontFamily: 'Plus Jakarta Sans' }}>Manage Subscription</Typography>
                          </Box> */}
                        </>
                      )}


                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1.2,
                          py: 0.7,
                          padding: '16px 16px',
                          borderBottomLeftRadius: 20,
                          borderBottomRightRadius: 20,
                          cursor: 'pointer',
                          transition: 'background 0.2s',
                          '&:hover': { background: '#FFF0F0' },
                        }}
                        onClick={logoutUser}
                      >
                        <LogoutIcon sx={{ fontSize: 20, color: '#F04438' }} />
                        <Typography sx={{ fontWeight: 500, fontSize: 15, color: '#F04438', fontFamily: 'Plus Jakarta Sans' }}>Logout</Typography>
                      </Box>
                    </Paper>
                  </ClickAwayListener>
                )}
              </>
            ) : (
              <Skeleton variant="rectangular" width={150} height={32} />
            )}
          </Box>
        </Toolbar>
      </AppBar>

      {/* <CreateAIEvaluatorModal
        isModalOpen={createAIEvaluatorOpen}
        onCancel={() => setCreateAIEvaluatorOpen(false)}
        onSuccess={() => {
          router.push(Routes.simulationEvaluator);
        }}
        initialData={null}
      /> */}
    </>
  );
};

export default Navbar;