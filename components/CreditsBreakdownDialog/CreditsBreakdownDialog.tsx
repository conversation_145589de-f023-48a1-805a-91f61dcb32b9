import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
  Box,
  Stack,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CallIcon from "@mui/icons-material/Call";
import MicIcon from "@mui/icons-material/Mic";
import ChatIcon from "@mui/icons-material/Chat";

interface CreditsBreakdownDialogProps {
  open: boolean;
  onClose: () => void;
}

export default function CreditsBreakdownDialog({
  open,
  onClose,
}: CreditsBreakdownDialogProps) {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="xs" // Adjust width as desired (e.g., "sm", "xs")
      sx={{
        "& .MuiPaper-root": {
          borderRadius: 2, // Slightly rounded corners
        },
      }}
    >
      {/* Dialog Title with a close (X) icon */}
      <DialogTitle
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          pb: 1,
        }}
      >
        <Typography variant="h6" component="div">
          Credits Usage Breakdown
        </Typography>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ ml: 2 }}
          edge="end"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ pt: 2 }}>
        {/* 1) Call Log Monitoring */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 2,
            borderRadius: 2,
            backgroundColor: "#f5f5f5",
            mb: 2,
          }}
        >
          <Stack direction="row" alignItems="center" spacing={1}>
            <CallIcon fontSize="small" />
            <Typography variant="body1">Call Log Monitoring</Typography>
          </Stack>
          <Typography variant="body2" color="text.secondary">
            1 Credit per Call Log
          </Typography>
        </Box>

        {/* 2) Voice Simulation */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 2,
            borderRadius: 2,
            backgroundColor: "#f5f5f5",
            mb: 2,
          }}
        >
          <Stack direction="row" alignItems="center" spacing={1}>
            <MicIcon fontSize="small" />
            <Typography variant="body1">Voice Simulation</Typography>
          </Stack>
          <Typography variant="body2" color="text.secondary">
            10 Credits per Minute
          </Typography>
        </Box>

        {/* 3) Text Simulation */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 2,
            borderRadius: 2,
            backgroundColor: "#f5f5f5",
            mb: 2,
          }}
        >
          <Stack direction="row" alignItems="center" spacing={1}>
            <ChatIcon fontSize="small" />
            <Typography variant="body1">Text Simulation</Typography>
          </Stack>
          <Typography variant="body2" color="text.secondary">
            3 Credits per Simulation
          </Typography>
        </Box>

        {/* Footer note */}
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ display: "block", textAlign: "center", mt: 2 }}
        >
          Credits are deducted based on your usage.
        </Typography>
      </DialogContent>
    </Dialog>
  );
}
