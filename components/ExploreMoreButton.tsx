import React from 'react';
import Button from '@mui/material/Button';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { SxProps, Theme } from '@mui/material/styles';

interface ExploreMoreButtonProps {
  onClick?: () => void;
  children?: React.ReactNode;
  sx?: SxProps<Theme>;
}

const ExploreMoreButton: React.FC<ExploreMoreButtonProps> = ({ onClick, children = 'Explore More', sx }) => (
  <Button
    variant="text"
    onClick={onClick}
    sx={{
      color: '#7F56D9',
      textTransform: 'none',
      fontFamily: 'Plus Jakarta Sans',
      fontWeight: 500,
      fontSize: '16px',
      padding: '8px 16px',
      gap: '2px',
      '&:hover': {
        backgroundColor: 'transparent',
        textDecoration: 'underline',
      },
      '& .MuiButton-endIcon': {
        marginLeft: '2px',
      },
      ...sx,
    }}
    endIcon={<KeyboardArrowRightIcon sx={{ fontSize: 12 }} />}
  >
    {children}
  </Button>
);

export default ExploreMoreButton; 