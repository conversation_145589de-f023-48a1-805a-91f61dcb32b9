"use client";

import React, { useState } from "react";
import { Popover as M<PERSON>Popover, Typography } from "@mui/material";
import { Tag } from "antd";

const BetaPopover = () => {
  const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);

  const handlePopoverOpen = (event: React.MouseEvent<HTMLDivElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  return (
    <>
      <div
        onMouseEnter={handlePopoverOpen}
        onMouseLeave={handlePopoverClose}
        style={{ display: "inline-block" }}
      >
        <Tag
          style={{
            fontSize: 10,
            lineHeight: "10px",
            height: 16,
            padding: "2px",
            cursor: "pointer",
          }}
        >
          Beta
        </Tag>
      </div>
      <MUIPopover
        sx={{
          pointerEvents: "none",
        }}
        open={open}
        anchorEl={anchorEl}
        onClose={handlePopoverClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
        disableRestoreFocus
      >
        <Typography sx={{ p: 1 }}>TestAI is in active development</Typography>
      </MUIPopover>
    </>
  );
};

export default BetaPopover;
