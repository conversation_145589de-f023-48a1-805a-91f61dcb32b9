.testimonialCarousel {
  width: 100%;
  padding: 2rem 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  background-image: url('/bg-overlay.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.indicatorContainer {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.indicator {
  width: 130px;
  height: 10px;
  border-radius: 16px;
  cursor: pointer;
  background-color: #e0e0e0;
  position: relative;
  overflow: hidden;
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    background-color: #915eff;
    transition: width 0.1s linear;
  }

  &--active {
    &::after {
      width: 100%;
      animation: progress 5s linear forwards;
    }
  }

  &--inactive {
    &::after {
      width: 0;
    }
  }
}

@keyframes progress {
  from {
    width: 0;
  }

  to {
    width: 100%;
  }
}

.testimonialCard {
  position: relative;
  width: 635px;
  background-image: url('/Group.svg');
  background-repeat: no-repeat;
  background-size: 16%;
  background-position: right -10px bottom 20px;
  border-radius: 64px;
  margin-left: 51px !important;
  margin-right: 51px !important;
  margin-bottom: 2rem;
  padding: 10px 0px 10px 0px;
  &__content {
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 220px;
    overflow: visible;
    position: relative;
  }

  &__imageContainer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 15px;
    padding-right: 50px;
    gap: 0.5rem;
  }

  &__imageWrapper {
    width: 120px;
    aspect-ratio: 2 / 1;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
  }

  &__quote {
    color: #fff !important;
    line-height: 1.5 !important;
    padding-left: 50px;
    padding-right: 50px;
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    font-family: 'Plus Jakarta Sans', sans-serif !important;
  }

  &__author {
    margin-top: 1.5rem;
    display: flex;
    flex-direction: column;
    font-family: 'Plus Jakarta Sans', sans-serif !important;
    gap: 0.25rem;
    padding-right: 50px;
  }

  &__name {
    color: #fff !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    font-family: 'ClashDisplay', sans-serif !important;
    line-height: 1.4 !important;
    margin: 0 !important;
    padding-left: 50px;
    padding-right: 50px;
  }

  &__role {
    color: #fff !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    font-family: 'ClashDisplay', sans-serif !important;
    line-height: 1.4 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.25rem !important;
    margin: 0 !important;
    padding-left: 50px;
    padding-right: 50px;

    &--company {
      font-weight: 700 !important;
      color: #fff !important;
    }
  }
}

.logosSection {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.logosTitle {
  color: #6c6c6c !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  font-family: 'Plus Jakarta Sans', sans-serif !important;
  margin: 0 !important;
}

.logosContainer {
  display: flex;
  gap: 54px;
  margin-top: 22px;
}

.logoItem {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
}

// Responsive styles
@media (max-width: 1200px) {
  .testimonialCard {
    width: 90%;
    max-width: 600px;
    margin-left: 20px !important;
    margin-right: 20px !important;
  }
}

@media (max-width: 768px) {
  .testimonialCarousel {
    padding: 1rem 0;
  }

  .testimonialCard {
    width: 95%;
    max-width: 500px;
    padding: 16px;
    border-radius: 32px;
    margin-left: 10px !important;
    margin-right: 10px !important;

    &__content {
      padding: 1.25rem;
      min-height: 180px;
    }

    &__quote {
      padding-left: 20px;
      padding-right: 20px;
      font-size: 16px !important;
      margin-bottom: 1.25rem !important;
    }

    &__name {
      font-size: 15px !important;
      padding-left: 20px;
      padding-right: 20px;
    }

    &__role {
      font-size: 13px !important;
      padding-left: 20px;
      padding-right: 20px;
    }

    &__imageContainer {
      padding-right: 20px;
    }
  }

  .indicator {
    width: 80px;
  }

  .logosContainer {
    gap: 24px;
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .testimonialCard {
    border-radius: 24px;

    &__content {
      padding: 1rem;
      min-height: 150px;
    }

    &__quote {
      padding-left: 10px;
      padding-right: 10px;
      font-size: 15px !important;
      margin-bottom: 1rem !important;
    }

    &__name {
      padding-left: 10px;
      padding-right: 10px;
      font-size: 14px !important;
    }

    &__role {
      padding-left: 10px;
      padding-right: 10px;
      font-size: 12px !important;
    }

    &__imageWrapper {
      width: 100px;
    }

    &__imageContainer {
      padding-right: 10px;
    }
  }

  .logosTitle {
    font-size: 16px !important;
  }

  .logosContainer {
    gap: 16px;
    margin-top: 16px;
  }

  .indicator {
    width: 60px;
    height: 8px;
  }
}
