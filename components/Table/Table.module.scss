@import '@/styles/variables.module';

.table {
  :global {
    .ant-table-thead {
      > tr {
        th {
          padding: 8px 24px !important;
        }
      }
    }

    .ant-table-tbody {
      .ant-table-row {
        .ant-table-cell.ant-table-row-expand-icon-cell {
          padding-left: 20px;
          padding-right: 0;

          .bx {
            margin-top: 5px;
          }
        }

        .ant-table-cell:nth-child(2) {
          padding-left: 12px;
        }
      }
    }

    .ant-table-expanded-row {
      > td {
        padding: 0 !important;
      }
    }

    .ant-descriptions-view {
      border: none !important;
    }

    .ant-pagination-prev {
      margin-right: auto !important;
    }

    .ant-pagination-next {
      margin-left: auto !important;
    }

    li.ant-pagination-item {
      cursor: pointer;
      a {
        color: $gray;
      }

      &.ant-pagination-item-active {
        border-color: #C7C7CC;

        a {
          color: $black;
        }
      }
    }
  }
}

.bx {
  font-size: 24px;
  cursor: pointer;
}

button.paginationButton {
  gap: 12px;
  cursor: pointer;

  :global {
    .ant-btn-icon {
      line-height: 1;
    }
  }
}