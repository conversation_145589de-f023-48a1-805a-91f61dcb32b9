import cn from "classnames";
import styles from "./Table.module.scss";
import {
  Button,
  ConfigProvider,
  PaginationProps,
  Table as AntdTable,
  TableProps,
} from "antd";
import { tableTheme } from "@/components/Table/constants";

const paginationItemRender: PaginationProps["itemRender"] = (
  _,
  type,
  originalElement,
) => {
  if (type === "prev") {
    return (
      <Button
        icon={<i className={cn(styles.bx, "bx bx-chevron-left")}></i>}
        size="large"
        className={styles.paginationButton}
      >
        Prev
      </Button>
    );
  }
  if (type === "next") {
    return (
      <Button
        icon={<i className={cn(styles.bx, "bx bx-chevron-right")}></i>}
        iconPosition="end"
        size="large"
        className={styles.paginationButton}
      >
        Next
      </Button>
    );
  }
  return originalElement;
};

export const Table = <T,>({ ...props }: TableProps<T>) => {
  return (
    <ConfigProvider theme={tableTheme}>
      <AntdTable
        size="large"
        className={styles.table}
        rootClassName={props.className}
        pagination={{
          position: ["bottomCenter"],
          itemRender: paginationItemRender,
          align: "center",
          responsive: true,
        }}
        {...props}
      />
    </ConfigProvider>
  );
};
