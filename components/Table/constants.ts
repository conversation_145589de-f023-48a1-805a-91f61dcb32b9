import { ThemeConfig } from "antd";
import variables from "@/styles/variables.module.scss";

export const tableTheme: ThemeConfig = {
  components: {
    Table: {
      cellFontSize: 16,
      cellPaddingBlock: 28,
      cellPaddingInline: 24,
      headerBg: "#F4F4F4",
      headerBorderRadius: 0,
      headerColor: variables.gray,
      headerSplitColor: "transparent",
      rowExpandedBg: "#F4F4F4",
    },
    Pagination: {
      itemSize: 40,
    },
  },
};
