"use client";
import React from "react";
import Image from "next/image";
import { Box } from "@mui/material";
import BetaPopover from "@/components/BetaPopover/BetaPopover";
import Routes from "@/constants/routes";
import styles from "./LogoBeta.module.scss";
import { useRouter } from "next/navigation";

export default function LogoBeta() {
    const router = useRouter();
    return (
        <Box className={styles.logoContainer}>
            <Image
                src="/logo-black.svg"
                width={100}
                height={60}
                alt="Logo of the application"
                onClick={() => router.push(Routes.scenario)}
                className={styles.logoImage}
            />
            <BetaPopover />
        </Box>
    );
};


