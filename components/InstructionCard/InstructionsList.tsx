"use client";

import React from "react";
import { Grid } from "@mui/material";
import InstructionCard from "./InstructionCard";
import { InstructionItem } from "@/types/InstructionItem";

interface TestingInstructionsProps {
  items: InstructionItem[];
}

export default function TestingInstructions({ items }: TestingInstructionsProps) {
  return (
    <Grid container spacing={2}>
      {items.map((item, index) => (
        <Grid item xs={12} md={4} key={index}>
          <InstructionCard item={item} />
        </Grid>
      ))}
    </Grid>
  );
}
