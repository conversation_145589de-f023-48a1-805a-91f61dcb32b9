"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardA<PERSON>,
  Typography,
  Button,
} from "@mui/material";
import { InstructionItem } from "@/types/InstructionItem";


interface InstructionCardProps {
  item: InstructionItem;
  backgroundColor?: string;
}

export default function InstructionCard({
  item,
}: InstructionCardProps) {
  return (
    <Card
      sx={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <CardContent sx={{ flexGrow: 1 }}>
        <Typography variant="h6" gutterBottom>
          {item.title}
        </Typography>
        <Typography variant="body2" color="inherit" sx={{ mb: 2 }}>
          {item.description}
        </Typography>
        <Typography variant="body2" color="inherit" sx={{ opacity: 0.7 }}>
          {item.docPrompt}
        </Typography>
        {/* <Link
          href={item.docLinkHref}

          target="_blank"
          rel="noopener"
        >
          {item.docLinkText}
        </Link> */}
        <Typography variant="body2"
          sx={{ color: "#a4a4ff" }}>
          {item.docLinkText}
        </Typography>
      </CardContent>
      <CardActions sx={{ mt: "auto", p: 2 }}>
        <Button
          variant="contained"
          color="primary"
          onClick={item.onButtonClick}
        >
          {item.buttonLabel}
        </Button>
      </CardActions>
    </Card>
  );
}
