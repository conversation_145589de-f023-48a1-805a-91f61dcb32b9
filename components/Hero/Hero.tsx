import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import RightSideHero from './RightSideIssues';
import styles from './Hero.module.scss'; 
import AddIcon from '@mui/icons-material/Add';
import Image from 'next/image';

interface HeroProps {
  onRunTest: () => void;
  testButtonText?: string;
}

export default function Hero({ onRunTest, testButtonText = 'Run My First Test' }: HeroProps) {
  return (
    <Box
      className={styles.heroContainer}
      sx={{
        position: 'relative',
        borderRadius: '16px',
        p: { xs: 3, md: 4 },
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        alignItems: 'flex-start',
        justifyContent: 'space-between',
        overflow: 'hidden',
      }}
    >
      <Box sx={{ maxWidth: 600, mb: { xs: 3, md: 0 } }}>
        <Typography
          sx={{
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: '800',
            color: '#FFFFFF',
            fontSize: '24px',
            mb: 2,
          }}
        >
          Click “Run My First Test” to Get Started
        </Typography>
        <Typography
          sx={{
            color: '#FFFFFF',
            fontFamily: 'Plus Jakarta Sans',
            fontSize: '16px',
            fontWeight: 500,
            mb: 3,
            lineHeight: 1.6,
          }}
        >
          Simulate real conversations, spot issues fast, and launch with confidence.
          Just plug in your agent by adding this number — we handle the rest.
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Button
            onClick={onRunTest}
            variant="outlined"
            sx={{
              color: '#7B61FF',
              backgroundColor: '#FFFFFF',
              borderRadius: '50px',
              border: '2px solid #FFFFFF',
              textTransform: 'none',
              fontWeight: 700,
              fontFamily: 'Plus Jakarta Sans',
              px: 3,
              py: 1,
              transition: '0.3s ease',
              '&:hover': {
                transform: 'scale(1.05)',
                border: '2px solid #DEB9FF',
                backgroundColor: '#ffff',
              },
            }}
          >
            <AddIcon sx={{ fontSize: 20, mr: '0.5rem' }} /> {testButtonText}
          </Button>
          <Typography
            variant="body2"
            sx={{
              color: '#ddd1f6',
              fontWeight: 700,
              display: 'flex',
              alignItems: 'center',
              fontFamily: 'Plus Jakarta Sans',
              gap: 1,
            }}
          >
            <Image src={'/Union.svg'} alt="" width={20} height={20} /> 
            <Box>one-click setup</Box>
          </Typography>
        </Box>
      </Box>

      <Box sx={{ zIndex: 2, ml: { md: 4 }, mt: { xs: 4, md: 0 } }}>
        <RightSideHero />
      </Box>
    </Box>
  );
}
