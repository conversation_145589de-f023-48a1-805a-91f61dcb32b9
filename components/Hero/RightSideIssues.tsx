import React from 'react';
import { Box, Chip } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import CheckIcon from '@mui/icons-material/Check';
import Image from 'next/image';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
const chipsData = [
  {
    label: 'Conflicting Contexts',
    icon: <Box sx={{ backgroundColor: '#FF5A5A', p: .1, borderRadius: 50, height: 16, width: 16, textAlign: 'center' }}><CloseIcon sx={{ color: '#fff', fontSize: 12 }} /></Box>,
    top: 90,
    right: 120,
    rotation: -0,
    backgroundColor: '#FF4C57',
  },
  {
    label: 'Ambiguous User Query',
    icon: <Box sx={{ backgroundColor: '#FF5A5A', p: .1, borderRadius: 50, height: 16, width: 16, textAlign: 'center' }}><CloseIcon sx={{ color: '#fff', fontSize: 12 }} /></Box>,
    top: 140,
    right: 310,
    rotation: -19.48,
    backgroundColor: '#FF4C57',
  },
  {
    label: 'Intent Recognised',
    icon: <Box sx={{ backgroundColor: '#00C853', p: .1, borderRadius: 50, height: 16, width: 16, textAlign: 'center' }}><CheckIcon sx={{ color: '#fff', fontSize: 12 }} /></Box>,
    top: 150,
    right: 240,
    rotation: -19.48,
    backgroundColor: '#1ECE7C',
  },
  {
    label: 'Redundant Prompt',
    icon: <Box sx={{ backgroundColor: '#FF5A5A', p: .1, borderRadius: 50, height: 16, width: 16, textAlign: 'center' }}><CloseIcon sx={{ color: '#fff', fontSize: 12 }} /></Box>,
    top: 125,
    right: 30,
    rotation: -0,
  },
  {
    label: 'Incorrect Intent Matched',
    icon: <Box sx={{ backgroundColor: '#FF5A5A', p: .1, borderRadius: 50, height: 16, width: 16, textAlign: 'center' }}><CloseIcon sx={{ color: '#fff', fontSize: 12 }} /></Box>,
    top: 160,
    right: 30,
    rotation: -0,
  },
];

export default function RightSideHero() {
  return (
    <Box
      sx={{
        position: 'relative',
        width: { xs: '100%', md: '340px' },
        minHeight: '180px', 
        backgroundColor: 'transparent',
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          right: 140,
          backgroundColor: 'rgba(0, 0, 0, 0.2)',
          borderRadius: '50px', 
          px: 2,
          py: 0.75,
          fontWeight: '700',
          fontSize: '14px',
          color: '#FFFFFF',
          display: 'flex',
          alignItems: 'center',
          zIndex: 3,
        }}
      >
        <AutoAwesomeIcon sx={{ color: '#fff', mr: 1.5 }}> </AutoAwesomeIcon> 27 Issues Found!
      </Box>

      <Box
        sx={{
          position: 'absolute',
          top: 40,
          right: 140,
          backgroundColor: '#000000',
          color: '#FFFFFF',
          borderTopLeftRadius: '10px',
          borderBottomLeftRadius: '10px',
          borderBottomRightRadius: '10px',
          p: 1,
          fontWeight: 'bold',
          fontSize: '14px',
          maxWidth: '200px',
          textAlign: 'center',
          zIndex: 3,
        }}
      >
        Solve these issues for me!
      </Box>

      <Image style={{
        position: 'absolute',
        top: 40,
        right: 100,
        borderTopLeftRadius: '10px',
        borderBottomLeftRadius: '10px',
        borderBottomRightRadius: '10px',
        fontWeight: 'bold',
        fontSize: '14px',
        maxWidth: '200px',
        textAlign: 'center',
        zIndex: 3,
      }} src={'/hero-vector.svg'} alt='' height={32} width={32} />
      {chipsData.map((chip, index) => (
        <Chip
          key={index}
          icon={chip.icon}
          label={chip.label}
          sx={{
            position: 'absolute',
            top: chip.top,
            right: chip.right,
            transform: `rotate(${chip.rotation}deg)`,
            backgroundColor: '#FFFFFF',
            color: '#2C2C2C',
            border: '1px solid #E0E0E0',
            borderRadius: '16px',
            fontSize: '14px',
            fontWeight: 500,
            px: 1.5,
            py: 0.5,
            zIndex: 2,
          }}
        />
      ))}
    </Box>
  );
}
