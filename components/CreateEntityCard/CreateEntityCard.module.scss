.card {
  height: 100%;
  border-radius: 24px !important;
  padding: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-color: #6941C6 !important;
  background-image: url('/Group.svg') !important;
  background-size: 40% !important;
  background-repeat: no-repeat !important;
  background-position: right !important;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
  &:hover {
    box-shadow: 0 4px 12px rgba(127, 86, 217, 0.2);
  }
}

.innerBox {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 1;
}

.centerBox {
  text-align: center;
}

.iconButton {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-bottom: 16px !important;
  &:hover {
    background-color: rgba(255, 255, 255, 0.2);
  }
}

.title {
  font-weight: 600 !important;
  font-size: 20px !important;
  line-height: 30px !important;
  color: white !important;
  font-family: 'Plus Jakarta Sans', sans-serif !important;
  margin-bottom: 8px !important;
}

.description {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 14px !important;
  line-height: 20px !important;
  font-family: 'Plus Jakarta Sans', sans-serif !important;
} 