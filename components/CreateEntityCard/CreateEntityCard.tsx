import React from "react";
import { Card, Box, Typography, IconButton } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import { SxProps, Theme } from "@mui/material/styles";
import styles from "./CreateEntityCard.module.scss";

interface CreateEntityCardProps {
  title: string;
  description: string;
  onClick: () => void;
  icon?: React.ReactNode;
  sx?: SxProps<Theme>;
}

const CreateEntityCard: React.FC<CreateEntityCardProps> = ({
  title,
  description,
  onClick,
  icon,
  sx,
}) => {
  return (
    <Card
      onClick={onClick}
      className={styles.card}
      style={sx as React.CSSProperties}
    >
      <Box className={styles.innerBox}>
        <Box className={styles.centerBox}>
          <IconButton className={styles.iconButton}>
            {icon || <AddIcon sx={{ color: '#fff', fontSize: 24 }} />}
          </IconButton>
          <Typography
            variant="h6"
            className={styles.title}
          >
            {title}
          </Typography>
          <Typography
            variant="body2"
            className={styles.description}
          >
            {description}
          </Typography>
        </Box>
      </Box>
    </Card>
  );
};

export default CreateEntityCard; 