"use client";
import React from 'react';
import {
    Box,
    Typography,
    IconButton,
} from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import { useOnboardingStore } from '@/stores/onboarding-store';

interface RightBottomDialogProps {
    children: React.ReactNode;
    open: boolean;
    title?: string;
    onClose: () => void;
    hideCloseButton?: boolean;
}

export default function RightBottomDialog({ children, open, onClose, hideCloseButton = false }: RightBottomDialogProps) {
    const [isExpanded, setIsExpanded] = React.useState(true);
    const { steps } = useOnboardingStore();

    const handleToggleExpand = () => {
        setIsExpanded(!isExpanded);
    };

    if (!open) return null;

    return (
        <Box
            sx={{
                position: 'fixed',
                bottom: 20,
                right: 20,
                width: 340,
                minHeight: isExpanded ? 200 : 80,
                p: 3,
                borderRadius: 10,
                border: '1px solid #7B61FF',
                fontFamily: 'Plus Jakarta Sans',
                bgcolor: '#FFFFFF',
                boxShadow: '0px 8px 24px rgba(0, 0, 0, 0.15)',
                backgroundClip: 'padding-box',
                transition: 'min-height 0.3s ease',
                zIndex: 1000,
                maxHeight: 'calc(100vh - 40px)',
                overflowY: 'auto',
                '&::-webkit-scrollbar': {
                    width: '4px',
                },
                '&::-webkit-scrollbar-track': {
                    background: '#f1f1f1',
                    borderRadius: '2px',
                },
                '&::-webkit-scrollbar-thumb': {
                    background: '#7B61FF',
                    borderRadius: '2px',
                },
            }}
        >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Box>
                    <Typography 
                        variant="subtitle1" 
                        sx={{ 
                            fontWeight: 'bold', 
                            fontFamily: 'Plus Jakarta Sans',
                            fontSize: '18px',
                            color: '#111827'
                        }}
                    >
                        Getting Started Guide
                    </Typography>
                    <Typography 
                        variant="body2" 
                        sx={{ 
                            fontFamily: 'Plus Jakarta Sans',
                            color: '#6B7280',
                            mt: 0.5
                        }}
                    >
                        To Start Your TestAI Journey
                    </Typography>
                </Box>
                <IconButton 
                    onClick={hideCloseButton ? handleToggleExpand : onClose} 
                    sx={{ color: '#242E2C', fontSize: '1.5rem' }}
                >
                    {hideCloseButton ? (
                        isExpanded ? <KeyboardArrowDownIcon /> : <KeyboardArrowUpIcon />
                    ) : (
                        <KeyboardArrowDownIcon />
                    )}
                </IconButton>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: isExpanded ? 1 : 0, gap: 1 }}>
                {[0, 1, 2].map((index) => (
                    <Box
                        key={index}
                        sx={{
                            flex: 1,
                            height: 8,
                            bgcolor: steps.createAgent && index === 0 ? '#7B61FF' :
                                    steps.createScenario && index === 1 ? '#7B61FF' :
                                    steps.runScenario && index === 2 ? '#7B61FF' : '#E0E0E0',
                            borderRadius: 2,
                            transition: 'background-color 0.3s ease',
                        }}
                    />
                ))}
            </Box>

            {isExpanded && children}
        </Box>
    );
}

