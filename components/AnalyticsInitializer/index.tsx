"use client";

import { useEffect } from "react";

// TODO: Remove this component after analytics is approved
export default function AnalyticsInitializer() {
  useEffect(() => {
    async function initAnalytics() {
      const { RudderAnalytics } = await import("@rudderstack/analytics-js");
      const analytics = new RudderAnalytics();

      analytics.load(
        process.env.NEXT_PUBLIC_RUDDERSTACK_WRITE_KEY as string,
        process.env.NEXT_PUBLIC_RUDDERSTACK_DATA_PLANE_URL as string,
      );

      analytics.ready(() => {
        analytics.track("Test Event", {
          category: "Test",
          label: "RudderStack Integration",
        });
      });
    }

    if (typeof window !== "undefined") {
      initAnalytics();
    }
  }, []);

  return null;
}
