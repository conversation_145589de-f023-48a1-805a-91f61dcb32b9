import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import Image from 'next/image';
import { styled } from '@mui/material/styles';

interface EmptyStateProps {
  icon: string;
  title: string;
  description: string;
  actionLabel?: string;
  onAction?: () => void;
}

const StyledContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(4),
  textAlign: 'center',
  minHeight: '300px',
  backgroundColor: '#fff',
  borderRadius: '24px',
  border: '1px solid #EAECF0',
}));

const StyledIconWrapper = styled(Box)({
  marginBottom: '24px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
});

const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  actionLabel,
  onAction,
}) => {
  return (
    <StyledContainer>
      <StyledIconWrapper>
        <Image src={icon} alt={title} width={80} height={80} />
      </StyledIconWrapper>
      <Typography
        variant="h4"
        sx={{
          fontSize: '24px',
          fontWeight: 800,
          color: '#101828',
          mb: 1,
          fontFamily: 'Plus Jakarta Sans',
        }}
      >
        {title}
      </Typography>
      <Typography
        variant="body1"
        sx={{
          color: '#667085',
          mb: actionLabel ? 3 : 0,
          fontFamily: 'Plus Jakarta Sans',
        }}
      >
        {description}
      </Typography>
      {actionLabel && onAction && (
        <Button
          variant="contained"
          onClick={onAction}
          sx={{
            mt: 2,
            backgroundColor: '#7F56D9',
            borderRadius: '8px',
            textTransform: 'none',
            fontFamily: 'Plus Jakarta Sans',
            '&:hover': {
              backgroundColor: '#6941C6',
            },
          }}
        >
          {actionLabel}
        </Button>
      )}
    </StyledContainer>
  );
};

export default EmptyState; 