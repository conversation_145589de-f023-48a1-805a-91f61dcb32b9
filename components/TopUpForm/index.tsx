import React, { useEffect, useRef, useState } from "react";
import {
  PaymentElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { <PERSON><PERSON>, Card, Flex, Form, Typography } from "antd";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import styles from "./styles.module.scss";
import { PricingPlan } from "@/app/(dashboard)/top-up/components/PricingPlans";
import useTopUpAnalytics from "@/app/(dashboard)/top-up/useTopUpAnalytics";

interface ICheckoutFormProps {
  selectedPlan: PricingPlan;
}

const CheckoutForm = ({ selectedPlan }: ICheckoutFormProps) => {
  const topUpAnalytics = useTopUpAnalytics();
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading] = useState(false);
  const notify = useNotification();
  const [paymentCompleted, setPaymentCompleted] = useState(false);
  const startTimeRef = useRef(Date.now());

  const handleSubmit = async () => {
    topUpAnalytics.trackCheckout();
    if (!elements || !stripe) return;

    const { error: submitError } = await elements.submit();
    if (submitError) {
      notify.error({
        message: "Failed to submit payment",
        description: submitError.message ?? "An error occurred",
      });
      return;
    }

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: process.env.NEXT_PUBLIC_APP_URL as string,
      },
      redirect: "if_required",
    });

    if (error) {
      notify.error({
        message: "Failed to confirm payment",
        description: error.message ?? "An error occurred",
      });
    } else {
      setPaymentCompleted(true);
      topUpAnalytics.trackSubscriptionUpgraded("Stripe", selectedPlan.title);
      topUpAnalytics.trackCreditPurchaseCompleted(
        selectedPlan.id,
        +selectedPlan.price,
        0,
      );
      notify.success({
        message: "Payment successful",
        description: "Your payment has been processed successfully",
      });
    }
  };

  useEffect(() => {
    const handleBeforeUnload = () => {
      if (!paymentCompleted) {
        const timeSpent = Date.now() - startTimeRef.current;
        topUpAnalytics.trackCreditPurchaseAbandoned(
          selectedPlan.id,
          "Top Up Form",
          timeSpent,
        );
      }
    };

    return () => {
      handleBeforeUnload();
    };
  }, []);

  return (
    <Card 
      size="default" 
      className={styles.card}
      styles={{
        body: {
          padding: '24px',
        }
      }}
    >
      <Form onFinish={handleSubmit}>
        <Flex vertical gap={32}>
          <div>
            <Typography.Title level={5} style={{ 
              marginBottom: '16px',
              color: '#101828',
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 600
            }}>
              Payment Details
            </Typography.Title>
            <PaymentElement />
          </div>
          <Button
            loading={isLoading}
            type="primary"
            htmlType="submit"
            className="checkout-button"
            disabled={!stripe || !elements}
            style={{
              height: '44px',
              fontSize: '16px',
              fontWeight: 600,
              fontFamily: 'Plus Jakarta Sans',
              backgroundColor: '#7F56D9',
              borderColor: '#7F56D9',
              borderRadius: '8px'
            }}
          >
            Pay ${selectedPlan.price}
          </Button>
        </Flex>
      </Form>
    </Card>
  );
};

export default CheckoutForm;
