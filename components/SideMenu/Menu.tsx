import React, { useState } from "react";
import {
  Box,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  Typography,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import cn from "classnames";

import SidebarMenu from "./SidebarMenu";
import UpdateAgentModal from "@/app/features/Agent/components/UpdateAgentModal/UpdateAgentModal";
import { useGeneralStore } from "@/providers/general-store-provider";
import { useAuthStore } from "@/stores/auth-store";
import styles from "./Menu.module.scss";
import useSectionEngagementAnalytics from "@/components/SideMenu/useSectionEngagementAnalytics";

interface IMenuProps {
  isMenuCollapsed?: boolean;
  className?: string;
  onItemClick?: () => void;
}

export const Menu = ({
  isMenuCollapsed = false,
  className,
  onItemClick,
}: IMenuProps) => {
  const sectionEngagementAnalytics = useSectionEngagementAnalytics();
  const [updateAgent, setUpdateAgent] = useState<string | null>(null);
  const { currentAgentId, setCurrentAgentId, agents } = useGeneralStore(
    (state) => state,
  );
  const { user } = useAuthStore((state) => state);

  const handleChange = (id: string) => {
    setCurrentAgentId(id);
  };

  const handleSelectChange = (event: SelectChangeEvent<string>) => {
    const id = event.target.value;
    sectionEngagementAnalytics.trackAgentSwitched(currentAgentId, id);
    if (id === "create") {
      setUpdateAgent("new");
    } else {
      handleChange(id);
    }
  };

  return (
    <Stack direction="column" spacing={3}>
      {!user?.isAdministrator && (
        <Stack
          direction="row"
          spacing={1}
          alignItems="center"
          padding={2}
          id="step1"
          sx={{ width: "100%" }}
        >
          <FormControl
            variant="outlined"
            size="small"
            sx={{ flexGrow: 1, justifyContent: "center" }}
          >
            <InputLabel id="agent-select-label">Agent</InputLabel>
            <Select
              labelId="agent-select-label"
              value={currentAgentId}
              onChange={handleSelectChange}
              label="Agent"
              sx={{ width: "100%" }}
              renderValue={(value) => {
                const selected = agents.find((agent) => agent.id === value);
                return selected ? selected.name : "";
              }}
              onClick={() =>
                sectionEngagementAnalytics.trackAgentSelectorClicked(
                  currentAgentId,
                  agents.length,
                )
              }
            >
              <MenuItem value="create">
                <Stack direction="row" spacing={1} alignItems="center">
                  <AddIcon fontSize="small" />
                  <Typography>Create</Typography>
                </Stack>
              </MenuItem>
              {agents.map((agent) => (
                <MenuItem key={agent.id} value={agent.id}>
                  {agent.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <IconButton
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              setUpdateAgent(currentAgentId);
            }}
            disabled={currentAgentId === "" || currentAgentId === "create"}
            sx={{ mr: 2 }}
          >
            <EditIcon fontSize="small" />
          </IconButton>
        </Stack>
      )}

      <Box className={cn(styles.menuWrapper, className)}>
        <SidebarMenu
          isMenuCollapsed={isMenuCollapsed}
          onItemClick={onItemClick}
        />
      </Box>

      {updateAgent && (
        <UpdateAgentModal
          agentId={updateAgent === "new" ? null : updateAgent}
          isModalOpen={!!updateAgent}
          onCancel={() => setUpdateAgent(null)}
        />
      )}
    </Stack>
  );
};
