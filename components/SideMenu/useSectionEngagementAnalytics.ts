import { useMemo } from "react";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import { useAuthStore } from "@/stores/auth-store";
import { usePathname } from "next/navigation";

const useSectionEngagementAnalytics = () => {
  const analytics = useRudderStackAnalytics();
  const { user } = useAuthStore((state) => state);
  const pathName = usePathname();

  return useMemo(() => {
    const track = (
      eventName: string,
      payload: Record<string, unknown> = {},
    ) => {
      if (!analytics) return;
      analytics.track(eventName, {
        user_id: user?.id,
        userId: user?.id,
        email: user?.email,
        domain: user?.email.split("@")[1],
        timestamp: new Date().toISOString(),
        app: "test.ai",
        category: payload.category
          ? (payload.category as string)
          : "Section Engagement",
        page_name: pathName,
        ...payload,
      });
    };

    return {
      trackPersonalitySectionClicked: (agent_id: string) =>
        track("personality_section_clicked", {
          agent_id,
          source_page: pathName,
        }),
      trackEvaluatorSectionClicked: (agent_id: string) =>
        track("evaluator_section_clicked", {
          agent_id,
          source_page: pathName,
        }),
      trackResultsSectionClicked: (agent_id: string) =>
        track("results_section_clicked", {
          agent_id,
          source_page: pathName,
        }),
      trackCallsSectionClicked: (agent_id: string) =>
        track("calls_section_clicked", {
          agent_id,
          source_page: pathName,
        }),
      trackOverviewSectionClicked: (agent_id: string) =>
        track("overview_section_clicked", {
          agent_id,
          source_page: pathName,
        }),

      trackTopUpClicked: (
        source_page: string,
        current_credit_balance: number,
      ) =>
        track("top_up_clicked", {
          source_page,
          current_credit_balance,
        }),

      trackAccountDropdownOpened: () =>
        track("account_dropdown_opened", {
          source_page: pathName,
        }),

      // Navigation & UI Interaction Events
      trackAgentSelectorClicked: (
        current_agent_id: string,
        agents_count: number,
      ) =>
        track("agent_selector_clicked", {
          current_agent_id,
          agents_count,
        }),
      trackAgentSwitched: (previous_agent_id: string, new_agent_id: string) =>
        track("agent_switched", {
          previous_agent_id,
          new_agent_id,
        }),
      trackSidebarSectionClicked: (
        section_name: string,
        previous_section: string,
      ) =>
        track("sidebar_section_clicked", {
          section_name,
          previous_section,
        }),
    };
  }, [analytics, user?.id]);
};

export default useSectionEngagementAnalytics;
