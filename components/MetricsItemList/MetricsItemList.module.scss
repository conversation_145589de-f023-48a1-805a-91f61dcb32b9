.metricsContainer {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 24px;
  overflow: auto;
}

.metricsTabs {
  display: flex;
  background: #fff;
  border: 2px solid #E4E7EC;
  border-radius: 32px;
  padding: 4px;
  margin-bottom: 24px;
  width: 100%;
}

.metricsTab {
  border: none;
  outline: none;
  background: transparent;
  color: #191A1D;
  font-weight: 700;
  font-size: 12px;
  border-radius: 24px;
  padding: 16px;
  cursor: pointer;
  transition: background 0.15s, color 0.15s;
  flex: 1;
}
.metricsTabSelected {
  background: #9E77ED;
  color: #fff;
  box-shadow: 0 2px 8px rgba(166,137,250,0.08);
}

.metricsSectionTitle {
  display: flex;
  align-items: center;
  font-size: 22px;
  font-weight: 700;
  color: #23272E;
  margin-bottom: 18px;
  margin-top: 8px;
  font-family: 'Plus Jakarta Sans', sans-serif;
}
.metricsSectionTitle .infoIcon {
  margin-left: 8px;
  width: 20px;
  height: 20px;
}

.metricsList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metricRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 500;
  color: #23272E;
  padding: 0 0 0 0;
  background: none;
}

.metricIcon {
  width: 36px;
  height: 36px;
  background: #EAFBF3;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.metricIcon svg {
  color: #3FC06D;
  font-size: 22px;
} 