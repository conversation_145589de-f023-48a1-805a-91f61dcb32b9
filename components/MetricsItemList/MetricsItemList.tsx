"use client";

import React, { useState, useMemo } from "react";
import styles from './MetricsItemList.module.scss';
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";

export interface ItemData {
  id: string;
  label: string;
  state: number;
}

export enum EFilterType {
  all = "Workflow Adherence",
  binary = "Binary",
  numeric = "Qualitative",
}
export type FilterType = keyof typeof EFilterType;

interface MetricsItemListProps {
  items: ItemData[];
}

export default function MetricsItemList({ items }: MetricsItemListProps) {
  const [filter, setFilter] = useState<FilterType>('all');

  const filteredItems = useMemo(() => {
    if (filter === 'all') return items;
    if (filter === 'binary') {
      return items.filter((item) => item.state === 0 || item.state === 1);
    }
    if (filter === 'numeric') {
      return items.filter((item) => item.state !== 0 && item.state !== 1);
    }
    return items;
  }, [filter, items]);

  return (
    <div className={styles.metricsContainer}>
      {/* Tabs */}
      <div className={styles.metricsTabs}>
        {(['all', 'binary', 'numeric'] as FilterType[]).map((key) => (
          <button
            key={key}
            className={
              styles.metricsTab + (filter === key ? ' ' + styles.metricsTabSelected : '')
            }
            onClick={() => setFilter(key)}
            type="button"
          >
            {EFilterType[key]}
          </button>
        ))}
      </div>
      {/* Section Title */}
      <div className={styles.metricsSectionTitle}>
        Workflow Adherence
        <span className={styles.infoIcon}><InfoOutlinedIcon fontSize="inherit" /></span>
      </div>
      {/* Metrics List */}
      <div className={styles.metricsList}>
        {filteredItems.length === 0 ? (
          <div style={{ color: '#8F96A3', fontSize: 16 }}>No metrics available</div>
        ) : (
          filteredItems.map((item) => (
            <div className={styles.metricRow} key={item.id}>
              <span>{item.label}</span>
              <span className={styles.metricIcon}>
                <CheckCircleOutlineIcon fontSize="inherit" />
              </span>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
