import React, { useState } from 'react';
import { Box, Button, Typography } from '@mui/material';

export default function ThreeButtonGroup() {
  const [selected, setSelected] = useState('agents');

  const getButtonStyles = (isSelected: boolean) => ({
    borderRadius: '9999px', 
    textTransform: 'none',
    fontSize: 14,
    fontWeight: isSelected ? 700 : 400,
    padding: '8px 20px',
    border: '1px solid',
    borderColor: isSelected ? '#7B61FF' : '#ECEBFF',
    backgroundColor: isSelected ? '#F4EBFF' : '#FFFFFF',
    color: isSelected ? '#7B61FF' : '#6B6B6B',
    '&:hover': {
      backgroundColor: isSelected ? '#F4EBFF' : '#F7F7F7',
      borderColor: isSelected ? '#7B61FF' : '#E0E0E0',
    },
  });

  return (
    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2, mt: 2 }}>
      <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
        <Typography variant="h5" sx={{ fontWeight: 700, mb: 1, fontFamily: 'Plus Jakarta Sans' }}>
          Test Templates
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            sx={getButtonStyles(selected === 'agents')}
            onClick={() => setSelected('agents')}
          >
            Agents
          </Button>

          <Button
            disabled
            sx={getButtonStyles(selected === 'metrics')}
          >
            <Box component="span" sx={{ display: 'flex', alignItems: 'center' }}>
              Metrics
              <Box
                component="span"
                sx={{
                  ml: 1,
                  fontSize: '10px',
                  backgroundColor: '#8C67DD',
                  color: '#FFFFFF',
                  borderRadius: '4px',
                  px: 0.5,
                  py: 0.25,
                }}
              >
                coming soon
              </Box>
            </Box>
          </Button>

          <Button
            disabled
            sx={getButtonStyles(selected === 'theme')}
          >
            <Box component="span" sx={{ display: 'flex', alignItems: 'center' }}>
              Theme
              <Box
                component="span"
                sx={{
                  ml: 1,
                  fontSize: '10px',
                  backgroundColor: '#8C67DD',
                  color: '#FFFFFF',
                  borderRadius: '4px',
                  px: 0.5,
                  py: 0.25,
                }}
              >
                coming soon
              </Box>
            </Box>
          </Button>
        </Box>
      </Box>
      <Box>
        {/* <Link href={''}>Explore More</Link> */}
      </Box>
    </Box>
  );
}
