import React, { useCallback, useState } from 'react';
import { 
  Box, 
  Typography, 
  CircularProgress, 
  Chip,
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import useFileUpload from '@/hooks/useFileUpload';
import { UploadedFile } from '@/hooks/useFileUpload';

interface FileUploadProps {
  onFileUploaded: (file: UploadedFile) => void;
  onFileRemoved: (fileId: number | string) => void;
  selectedFiles: UploadedFile[];
  agentId?: string;
}


const FileUpload: React.FC<FileUploadProps> = ({
  onFileUploaded,
  selectedFiles,
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const { uploadFile, isUploading, uploadProgress } = useFileUpload();
  // const { dataSet, fetchDataSet } = useDataSet();

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    for (const file of files) {
      const uploadedFile = await uploadFile(file);
      if (uploadedFile) {
        onFileUploaded(uploadedFile);
        // fetchDataSet(); // Refresh the data set after upload
      }
    }
  }, [uploadFile, onFileUploaded]);

  const handleFileInput = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    for (const file of files) {
      const uploadedFile = await uploadFile(file);
      if (uploadedFile) {
        onFileUploaded(uploadedFile);
        // fetchDataSet(); // Refresh the data set after upload
      }
    }
  }, [uploadFile, onFileUploaded]);

  // const handleSelectChange = (event: SelectChangeEvent<number[]>) => {
  //   const value = event.target.value as number[];
    
  //   // Find files that were removed
  //   const removedFiles = selectedFiles.filter(file => !value.includes(file.id));
  //   removedFiles.forEach(file => onFileRemoved(file.id));
    
  //   // Find files that were added
  //   const addedFiles = dataSet.filter(file => 
  //     value.includes(file.id) && !selectedFiles.find(f => f.id === file.id)
  //   );
  //   addedFiles.forEach(file => onFileUploaded(file));
  // };


  return (
    <Box sx={{ width: '100%' }}>
      {/* Upload Area */}
      <Box
        sx={{
          width: '100%',
          minHeight: '200px',
          border: '2px dashed',
          borderColor: isDragging ? '#7F56D9' : 'grey.300',
          borderRadius: '12px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          p: 3,
          position: 'relative',
          backgroundColor: isDragging ? '#F9F5FF' : 'background.paper',
          transition: 'all 0.3s ease',
          mb: 2,
          '&:hover': {
            borderColor: '#7F56D9',
            backgroundColor: '#F9F5FF',
          },
        }}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {isUploading ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
            <CircularProgress variant="determinate" value={uploadProgress} sx={{ color: '#7F56D9' }} />
            <Typography variant="body2" color="text.secondary">
              Uploading... {uploadProgress}%
            </Typography>
          </Box>
        ) : (
          <>
            <CloudUploadIcon sx={{ fontSize: 48, color: '#101828', mb: 2 }} />
            <Typography variant="h6" gutterBottom sx={{ color: '#101828' }}>
              Drag & Drop PDF files here
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              or click to browse files
            </Typography>
            <input
              type="file"
              accept=".pdf"
              multiple
              onChange={handleFileInput}
              style={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                top: 0,
                left: 0,
                opacity: 0,
                cursor: 'pointer',
              }}
            />
          </>
        )}
      </Box>

      {/* Data Set Selection */}
      {/* <FormControl fullWidth>
        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
          <Typography
            variant="subtitle1"
            sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
          >
            Select from Data Set
          </Typography>
          <Tooltip
            sx={{ fontFamily: "Plus Jakarta Sans" }}
            title="Select PDF files from the available data set"
            placement="right"
          >
            <InfoOutlinedIcon fontSize="small" sx={{ ml: 1 }} />
          </Tooltip>
        </Box>
        <Select
          labelId="data-set-select-label"
          id="data-set-select"
          multiple
          sx={{
            borderRadius: '16px',
          }}
          value={selectedFiles.map(file => file.id)}
          onChange={handleSelectChange}
          input={<OutlinedInput />}
          renderValue={(selected) => (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {selected.map((value) => {
                const file = dataSet.find(f => f.id === value);
                return file ? (
                  <Chip
                    key={file.id}
                    label={file.file_name}
                    sx={{ m: 0.5 }}
                  />
                ) : null;
              })}
            </Box>
          )}
          MenuProps={{
            ...MenuProps,
            PaperProps: {
              sx: {
                '& .MuiMenuItem-root': {
                  '&.Mui-selected, &.Mui-selected:hover': {
                    backgroundColor: '#6941C6',
                    color: '#fff',
                  },
                  '&:hover': {
                    backgroundColor: '#F4EBFF',
                  },
                },
              },
            },
          }}
        >
          {dataSet.map((file) => (
            <MenuItem key={file.id} value={file.id}>
              <Checkbox checked={selectedFiles.some(f => f.id === file.id)} />
              <ListItemText primary={file.file_name} />
            </MenuItem>
          ))}
        </Select>
      </FormControl> */}

      {/* Selected Files Display */}
      {selectedFiles.length > 0 && (
        <Box sx={{ width: '100%', mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Selected Files:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {selectedFiles.map((file) => (
              <Chip
                key={file.id}
                label={file.file_name}
                sx={{ m: 0.5 }}
              />
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default FileUpload; 