import React from "react";
import { Button } from "@mui/material";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";

interface BreakdownInfoCardProps {
    onOpenBreakdown: () => void;
}

export default function BreakdownInfoButton({ onOpenBreakdown }: BreakdownInfoCardProps) {
    return (
        <Button variant="contained" color="primary" startIcon={<MonetizationOnIcon />} onClick={onOpenBreakdown}>
            Credit Breakdown
        </Button>
    );
}
