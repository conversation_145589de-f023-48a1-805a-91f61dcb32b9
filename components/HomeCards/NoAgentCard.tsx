import React from "react";
import {
  Box,
  Typography,
  Button
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import Image from "next/image";

interface NoAgentsProps {
  onCreateAgent: () => void;
}

export default React.memo(function NoAgentsCard({ onCreateAgent }: NoAgentsProps) {
  return (
    <Box
      sx={{
        width: "100%",
        height: 370,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        bgcolor: "#fff",
        borderRadius: 4,
      }}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          bgcolor: "#fff",
          p: 4,
          borderRadius: 4,
          minWidth: 400,
        }}
      >
        {/* Icon */}
        <Box
          sx={{
            bgcolor: "#F4EBFF",
            borderRadius: "16px",
            width: 80,
            height: 80,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            mb: 2,
          }}
        >
          <Image src="/ai-scan-empty.svg" alt="AI Scan" width={57} height={57} />
        </Box>
        {/* Title */}
        <Typography variant="h6" fontWeight={700} mb={0.5} color="#101828" align="center" sx={{
          fontFamily: 'Plus Jakarta Sans'
        }}>
          No Agents Found
        </Typography>
        {/* Subtitle */}
        <Typography variant="body2" sx={{ fontFamily: 'Plus Jakarta Sans' }} color="#667085" mb={3} align="center">
          Start and create a new one.
        </Typography>
        {/* Button */}
        <Button
          variant="contained"
          sx={{
            bgcolor: "#7F56D9",
            borderRadius: "16px",
            textTransform: "none",
            fontWeight: 600,
            height: '52px',
            fontSize: 14,
            px: 3,
            width: 225,
            boxShadow: "none",
            fontFamily: 'Plus Jakarta Sans',
            "&:hover": { bgcolor: "#6941C6" },
          }}
          startIcon={<AddIcon sx={{ color: "#fff" }} />}
          onClick={onCreateAgent}
        >
          Add a new agent
        </Button>
      </Box>
    </Box>
  );
});
