import React from "react";
import { Card, CardContent, Typography, Box, LinearProgress } from "@mui/material";

interface CallsCardProps {
  currentCalls: number;
  totalCalls: number;
}

export default function CallsCard({ currentCalls, totalCalls }: CallsCardProps) {
  const progress = (currentCalls / totalCalls) * 100;
  return (
    <Card sx={{ height: "100%", display: "flex", flexDirection: "column", justifyContent: "space-between", p: 2 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Number of calls
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Lorem ipsum dolor, sit amet consectetur adipisicing elit.
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2, mt: 1 }}>
          <Typography variant="body1">
            {currentCalls}/{totalCalls}
          </Typography>
          <Box sx={{ flexGrow: 1 }}>
            <LinearProgress variant="determinate" value={progress} sx={{ height: 10, borderRadius: 5 }} />
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
}
