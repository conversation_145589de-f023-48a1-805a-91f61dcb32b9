import React from "react";
import { Card, CardContent, CardActions, Typography } from "@mui/material";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import DashboardButton from "../DashboardButton/DashboardButton";

interface SubscriptionCardProps {
  onUpgrade: () => void;
}

export default function SubscriptionCard({ onUpgrade }: SubscriptionCardProps) {
  return (
    <Card sx={{ height: "100%", display: "flex", flexDirection: "column", justifyContent: "space-between", p: 2 }}>
      <CardContent>
        <Typography gutterBottom sx={{ color: "text.secondary", fontSize: 14 }}>
          Current Subscription Plan
        </Typography>
        <Typography variant="h6" gutterBottom>
          Free Plan
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Enjoy essential features at no cost with our Free Plan.
        </Typography>
      </CardContent>
      <CardActions>
        <DashboardButton label=" Upgrade" variant="contained" color="primary" startIcon={<ArrowUpwardIcon />} onClick={onUpgrade} />
      </CardActions>
    </Card>
  );
}
