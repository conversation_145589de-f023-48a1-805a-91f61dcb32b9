import { Box, CircularProgress } from '@mui/material';

interface LoadingSpinnerProps {
    fullScreen?: boolean;
}

export const LoadingSpinner = ({ fullScreen = false }: LoadingSpinnerProps) => {
    const spinner = (
        <CircularProgress 
            size={40} 
            sx={{ 
                color: '#7F56D9',
                '&.MuiCircularProgress-root': {
                    animationDuration: '550ms',
                }
            }} 
        />
    );

    if (fullScreen) {
        return (
            <Box
                sx={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: 'rgba(255, 255, 255, 0.8)',
                    zIndex: 9999
                }}
            >
                {spinner}
            </Box>
        );
    }

    return (
        <Box
            sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                p: 3
            }}
        >
            {spinner}
        </Box>
    );
}; 