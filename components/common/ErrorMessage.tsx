import { Box, Typography, Button } from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';

interface ErrorMessageProps {
    message: string;
    onRetry?: () => void;
    fullScreen?: boolean;
}

export const ErrorMessage = ({ message, onRetry, fullScreen = false }: ErrorMessageProps) => {
    const errorContent = (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: 2,
                p: 3
            }}
        >
            <Typography 
                variant="h6" 
                color="error"
                sx={{ textAlign: 'center' }}
            >
                {message}
            </Typography>
            {onRetry && (
                <Button
                    variant="contained"
                    startIcon={<RefreshIcon />}
                    onClick={onRetry}
                    sx={{
                        bgcolor: '#7F56D9',
                        '&:hover': {
                            bgcolor: '#6941C6'
                        }
                    }}
                >
                    Try Again
                </Button>
            )}
        </Box>
    );

    if (fullScreen) {
        return (
            <Box
                sx={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: '#F9FAFB'
                }}
            >
                {errorContent}
            </Box>
        );
    }

    return errorContent;
}; 