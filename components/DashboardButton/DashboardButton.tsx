import React from "react";
import { Button } from "@mui/material";

export interface DashboardButtonProps {
  variant?: "contained" | "outlined" | "text";
  color?:
    | "primary"
    | "secondary"
    | "error"
    | "info"
    | "success"
    | "warning"
    | "inherit";
  startIcon?: React.ReactNode;
  label: string;
  onClick: () => void;
  fullWidth?: boolean;
  sx?: object;
}

const DashboardButton: React.FC<DashboardButtonProps> = ({
  variant = "contained",
  color = "primary",
  startIcon,
  label,
  onClick,
  fullWidth = false,
  sx = {},
}) => {
  return (
    <Button
      variant={variant}
      color={color}
      startIcon={startIcon}
      onClick={onClick}
      fullWidth={fullWidth}
      sx={sx}
    >
      {label}
    </Button>
  );
};

export default DashboardButton;
