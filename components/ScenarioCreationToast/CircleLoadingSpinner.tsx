import React from 'react';
import styles from './CircleLoadingSpinner.module.scss';

interface CircleLoadingSpinnerProps {
  progress?: number;
}

const CircleLoadingSpinner: React.FC<CircleLoadingSpinnerProps> = () => {
  return (
    <div className={styles.spinnerContainer}>
      <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg" className={styles.spinner}>
        <path
          d="M13.0007 2.20117C14.419 2.20117 15.8233 2.48052 17.1337 3.02327C18.444 3.56602 19.6346 4.36155 20.6374 5.36442C21.6403 6.36729 22.4358 7.55788 22.9786 8.86819C23.5213 10.1785 23.8007 11.5829 23.8007 13.0012C23.8007 14.4195 23.5213 15.8238 22.9786 17.1342C22.4358 18.4445 21.6403 19.6351 20.6374 20.6379C19.6346 21.6408 18.444 22.4363 17.1337 22.9791C15.8233 23.5218 14.419 23.8012 13.0007 23.8012C11.5824 23.8012 10.178 23.5218 8.8677 22.9791C7.55738 22.4363 6.3668 21.6408 5.36393 20.6379C4.36105 19.635 3.56553 18.4445 3.02278 17.1341C2.48003 15.8238 2.20068 14.4194 2.20068 13.0012C2.20068 11.5829 2.48004 10.1785 3.02279 8.86819C3.56554 7.55787 4.36106 6.36729 5.36393 5.36441C6.36681 4.36154 7.55739 3.56602 8.86771 3.02327C10.178 2.48052 11.5824 2.20117 13.0007 2.20117L13.0007 2.20117Z"
          stroke="white"
          strokeOpacity="0.2"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.0007 2.20117C15.2814 2.20117 17.5036 2.92321 19.3488 4.26379C21.1939 5.60437 22.5673 7.49468 23.2721 9.66379C23.9769 11.8329 23.9769 14.1694 23.2721 16.3386C22.5673 18.5077 21.1939 20.398 19.3488 21.7386"
          stroke="#915EFF"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
          className={styles.spinnerPath}
        />
      </svg>
    </div>
  );
};

export default CircleLoadingSpinner;
