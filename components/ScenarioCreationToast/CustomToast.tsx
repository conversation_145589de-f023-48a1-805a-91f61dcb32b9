import React, { useEffect, useState } from 'react';
import { Box, Typography } from '@mui/material';
import styles from './CustomToast.module.scss';
import CircleLoadingSpinner from './CircleLoadingSpinner';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

import { ScenarioCreationStatus } from '@/stores/scenario-creation-store';

interface CustomToastProps {
  status: ScenarioCreationStatus;
  message?: string;
  onClose?: () => void;
  autoHideDuration?: number;
}

const CustomToast: React.FC<CustomToastProps> = ({
  status,
  message,
  onClose,
  autoHideDuration = 2500,
}) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    if (status !== 'creating' && autoHideDuration > 0) {
      const timer = setTimeout(() => {
        setVisible(false);
        if (onClose) onClose();
      }, autoHideDuration);

      return () => clearTimeout(timer);
    }
  }, [status, autoHideDuration, onClose]);

  if (!visible) return null;

  let icon;
  let text;

  switch (status) {
    case 'creating':
      icon = <CircleLoadingSpinner />;
      text = 'Generating Scenarios';
      break;
    case 'success':
      icon = <CheckCircleOutlineIcon className={styles.successIcon} />;
      text = message || 'Scenarios Generated Successfully';
      break;
    case 'error':
      icon = <ErrorOutlineIcon className={styles.errorIcon} />;
      text = message || 'Failed to generate scenarios';
      break;
    case 'idle':
    default:
      return null; // Don't show toast in idle state
  }

  return (
    <Box className={styles.toastWrapper}>
      <Box className={styles.toastContainer}>
        <Box className={styles.innerContainer}>
          {icon}
          <Typography className={styles.toastText}>
            {text}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default CustomToast;
