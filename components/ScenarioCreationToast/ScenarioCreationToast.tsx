import React from 'react';
import { useScenarioCreationStore } from '@/stores/scenario-creation-store';
import CustomToast from './CustomToast';

interface ScenarioCreationToastProps {
  onClose?: () => void;
}

const ScenarioCreationToast: React.FC<ScenarioCreationToastProps> = ({ onClose }) => {
  const { status, error, toastVisible } = useScenarioCreationStore();

  if (!toastVisible) return null;

  return (
    <CustomToast
      status={status}
      message={error || undefined}
      onClose={onClose}
      autoHideDuration={status !== 'creating' ? 2500 : 0}
    />
  );
};

export default ScenarioCreationToast;
