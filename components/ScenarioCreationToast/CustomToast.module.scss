.toastWrapper {
  position: fixed;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.toastContainer {
  min-width: 192px;
  height: 36px;
  padding: 6px 16px 6px 6px;
  position: relative;
  background: #1B1B1B;
  border-radius: 200px;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.innerContainer {
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  display: flex;
}

.toastText {
  color: white;
  font-size: 16px;
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 500;
  line-height: 24px;
  word-wrap: break-word;
}

.successIcon {
  color: #4CAF50;
  font-size: 24px;
}

.errorIcon {
  color: #F44336;
  font-size: 24px;
}
