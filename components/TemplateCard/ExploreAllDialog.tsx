import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
  Box,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import TemplateCardList from './TemplateCardList';
import { TemplateCardData } from './TemplateCard';

interface ExploreAllDialogProps {
  open: boolean;
  onClose: () => void;
  items: TemplateCardData[];
  onItemSelect: (item: TemplateCardData) => void;
  onDelete?: (id: string) => void;
  title: string;
  type: 'agents' | 'templates';
}

export default function ExploreAllDialog({
  open,
  onClose,
  items,
  onItemSelect,
  onDelete,
  title,
  type
}: ExploreAllDialogProps) {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '24px',
          p: 2,
          maxWidth: '1200px'
        }
      }}
    >
      <DialogTitle sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 700,
                fontSize: '24px',
                color: '#101828',
                fontFamily: 'Plus Jakarta Sans',
              }}
            >
              {title}
            </Typography>
          </Box>
          <IconButton
            onClick={onClose}
            sx={{
              color: '#667085',
              '&:hover': {
                backgroundColor: '#F9FAFB',
              },
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ p: 2 }}>
        <TemplateCardList
          templates={items}
          onTemplateSelect={onItemSelect}
          onDelete={onDelete}
          isTemplateList={type === 'templates'}
        />
      </DialogContent>
    </Dialog>
  );
} 