import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import TemplateCardList from "./TemplateCardList";
import { TemplateCardData } from "./TemplateCard";

interface AllTemplatesDialogProps {
  open: boolean;
  onClose: () => void;
  templates: TemplateCardData[];
  onTemplateSelect: (template: TemplateCardData) => void;
}

const AllTemplatesDialog: React.FC<AllTemplatesDialogProps> = ({
  open,
  onClose,
  templates,
  onTemplateSelect,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: "12px",
          backgroundColor: "#FFFFFF",
          minHeight: "80vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          p: 2,
          borderBottom: "none",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            fontWeight: 700,
            fontFamily: "Plus Jakarta Sans",
            color: "#101828",
          }}
        >
         Popular Agent Templates
        </Typography>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ color: "text.secondary" }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        <TemplateCardList
          templates={templates}
          onTemplateSelect={onTemplateSelect}
          isTemplateList={true}
        />
      </DialogContent>
    </Dialog>
  );
};

export default AllTemplatesDialog; 