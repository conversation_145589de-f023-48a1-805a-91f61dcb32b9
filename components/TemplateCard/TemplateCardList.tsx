import React, { useState } from 'react';
import TemplateCard, { TemplateCardData } from './TemplateCard';
import { Box, Grid } from "@mui/material";

interface TemplateCardListProps {
  templates: TemplateCardData[];
  onTemplateSelect?: (data: TemplateCardData) => void;
  onDelete?: (id: string) => void;
  isTemplateList?: boolean;
  hideActions?: boolean;
}

const TemplateCardList: React.FC<TemplateCardListProps> = ({ 
  templates, 
  onTemplateSelect, 
  onDelete,
  isTemplateList = false,
  hideActions
}) => {
  const [, setSelectedId] = useState<string | null>(null);

  const handleSelect = (data: TemplateCardData) => {
    setSelectedId(data.id);
    if (onTemplateSelect) {
      onTemplateSelect(data);
    }
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Grid container spacing={2}>
        {templates.map((template) => (
          <Grid item xs={12} sm={6} md={4} key={template.id}>
            <TemplateCard
              data={{
                ...template,
                isCreateCard: isTemplateList
              }}
              onSelect={isTemplateList ? () => handleSelect(template) : undefined}
              onDelete={onDelete}
              hideActions={hideActions}
            />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default TemplateCardList;
