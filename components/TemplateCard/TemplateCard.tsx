import React from 'react';
import { Box, Stack, Typography, <PERSON>ltip, IconButton, Menu, MenuItem, ListItemIcon } from "@mui/material";
import Image from "next/image";
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import EditIcon from '@mui/icons-material/Edit';
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import DeleteIcon from '@mui/icons-material/Delete';
import { useGeneralStore } from "@/providers/general-store-provider";

export interface TemplateCardData {
  id: string;
  isCreateCard?: boolean;
  name: string;
  description: string;
  metricsCount: number;
  metrics: string[];
  moreCount: number;
  currentAgentId?: string;
}

interface TemplateCardProps {
  data: TemplateCardData;
  onSelect?: () => void;
  onDelete?: (id: string) => void;
  hideActions?: boolean;
}

export default function TemplateCard({ data, onSelect, onDelete, hideActions }: TemplateCardProps) {
  const {
    name,
    description,
    metricsCount,
    metrics,
    moreCount,
    currentAgentId,
    id
  } = data;

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const { setCurrentAgentId } = useGeneralStore((state) => state);
  const isCurrentAgent = currentAgentId === id;

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEditClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    if (onSelect) {
      onSelect();
    }
    handleMenuClose();
  };

  const handleSwitchAgent = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setCurrentAgentId(id);
    handleMenuClose();
  };

  const handleDeleteAgent = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    if (onDelete) {
      onDelete(id);
    }
    handleMenuClose();
  };

  return (
    <Box
      onClick={data.isCreateCard ? onSelect : undefined}
      sx={{
        height: '100%',
        minHeight: '230px',
        borderRadius: '24px',
        backgroundColor: '#fff',
        p: 3,
        display: "flex",
        flexDirection: "column",
        cursor: data.isCreateCard ? "pointer" : "default",
        transition: 'all 0.2s ease-in-out',
        position: 'relative',
        border: isCurrentAgent ? '2px solid #7F56D9' : '1px solid #E3E3E3',
        '&:hover': {
          border: isCurrentAgent ? '2px solid #7F56D9' : '1px solid #7F56D9',
        }
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2.5 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Image src="/audio-icon.svg" alt="Agent Icon" width={52} height={52} />
        </Box>
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 1,
        }}>
          {metricsCount > 0 && (
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: 1,
              backgroundColor: "#F9F5FF",
              borderRadius: "16px",
            padding: "4px 12px",
          }}>
            <Image src="/metrics-vector.svg" alt="Metrics Icon" width={16} height={16} />
            <Typography
              sx={{
                color: "#7F56D9",
                fontWeight: 600,
                fontFamily: "Plus Jakarta Sans",
                fontSize: "14px",
              }}
            >
              {metricsCount} Metrics
            </Typography>
            </Box>
          )}
          {isCurrentAgent && (
              <Box
                sx={{
                  backgroundColor: '#7F56D9',
                  color: 'white',
                  borderRadius: "16px",
                  padding: "4px 12px",
                  fontSize: '14px',
                  fontWeight: 600,
                  fontFamily: 'Plus Jakarta Sans',
                  ml: 1,
                }}
              >
                Current
              </Box>
            )}
        </Box>
      </Box>

      <Box sx={{ mb: 2.5 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography 
            variant="h6" 
            sx={{ 
              fontWeight: 600,
              fontSize: "20px",
              lineHeight: "30px",
              color: "#101828",
              fontFamily: "Plus Jakarta Sans"
            }}
          >
            {name}
          </Typography>
          {!data.isCreateCard && !hideActions && (
            <IconButton 
              onClick={handleMenuClick}
              sx={{ 
                color: '#667085',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                }
              }}
            >
              <MoreHorizIcon />
            </IconButton>
          )}
        </Box>
        <Typography 
          variant="body2" 
          sx={{ 
            color: "#475467",
            fontSize: "14px",
            lineHeight: "20px",
            fontFamily: "Plus Jakarta Sans",
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {description}
        </Typography>
      </Box>

      <Box sx={{ mt: 'auto' }}>
        {metrics && metrics.length > 0 && (
          <>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1.5 }}>
              <Typography
                sx={{
                  fontSize: "14px",
                  fontWeight: 600,
                  color: "#101828",
                  fontFamily: "Plus Jakarta Sans"
                }}
              >
                What We Will Test For &quot;Metrics&quot;
              </Typography>
              <Tooltip title="These are the metrics we'll evaluate during testing">
                <IconButton size="small" sx={{ p: 0.5 }}>
                  <HelpOutlineIcon sx={{ fontSize: 16, color: '#667085' }} />
                </IconButton>
              </Tooltip>
            </Box>
            
            <Stack direction="row" gap={.5} flexWrap="wrap" sx={{ border: "1px solid #E3E3E3", p: .8, borderRadius: "14px" }}>
              {metrics.map((metric, index) => (
                <Box
                  key={index}
                  sx={{
                    backgroundColor: "#f2f2f2",
                    px: .5,
                    py: .5,
                    borderRadius: "16px",
                    fontSize: "11px",
                    color: "#344054",
                    fontWeight: 500,
                    marginLeft: "0 !important",
                    fontFamily: "Plus Jakarta Sans",
                    border: "1px solid #EAECF0"
                  }}
                >
                  {metric}
                </Box>
              ))}
              {moreCount > 0 && (
                <Box 
                  sx={{ 
                    backgroundColor: "#f2f2f2",
                    px: .5,
                    py: .5,
                    borderRadius: "16px",
                    fontSize: "11px",
                    color: "#344054",
                    fontWeight: 500,
                    fontFamily: "Plus Jakarta Sans",
                    border: "1px solid #EAECF0"
                  }}
                >
                  +{moreCount} More
                </Box>
              )}
            </Stack>
          </>
        )}
      </Box>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        onClick={(e) => e.stopPropagation()}
        PaperProps={{
          sx: {
            borderRadius: '14px',
            mt: 1,
          }
        }}
      >
        <MenuItem onClick={handleEditClick}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          Edit
        </MenuItem>
        {!isCurrentAgent && (
          <MenuItem onClick={handleSwitchAgent}>
            <ListItemIcon>
              <SwapHorizIcon fontSize="small" />
            </ListItemIcon>
            Switch
          </MenuItem>
        )}
        <MenuItem onClick={handleDeleteAgent} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" sx={{ color: 'error.main' }} />
          </ListItemIcon>
          Delete
        </MenuItem>
      </Menu>
    </Box>
  );
}
