.card {
  background-color: #ffffff;
  padding: 1.5rem; // p-6
  border-radius: 0.5rem; // rounded-lg
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); // shadow-lg
  display: flex;
  flex-direction: column;
  gap: 1rem; // gap-4
  max-width: 24rem; // max-w-sm
  border: 1px solid #e5e7eb; // border-gray-200
  font-family: "Roboto Thin", sans-serif;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title {
  font-size: 1.25rem; // text-xl
  font-weight: bold;
}

.progressBarWrapper {
  margin-bottom: 1rem; // mb-4
  background-color: #e5e7eb; // bg-gray-200
  border-radius: 9999px; // rounded-full
  height: 0.625rem; // h-2.5 (~10px)
}

.progressBar {
  background-color: #2563eb; // bg-blue-600
  height: 0.625rem; // h-2.5
  border-radius: 9999px; // rounded-full
}

.controls {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
}

.content {
  width: 20rem;
}

.buttonPrev {
  background-color: #e5e7eb;
  color: #1f2937;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
}

.buttonNext {
  background-color: #60a5fa;
  color: #1f2937;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
}

.buttonSkip {
  background-color: #94a3b8;
  color: #1f2937;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
}
