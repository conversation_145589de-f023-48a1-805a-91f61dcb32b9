"use client";

import { CardComponentProps } from "nextstepjs";
import styles from "./styles.module.scss";
import { Button } from "antd";

const CustomTourCard: React.FC<CardComponentProps> = ({
  step,
  currentStep,
  totalSteps,
  nextStep,
  prevStep,
  skipTour,
  arrow,
}) => {
  return (
    <div className={styles.card}>
      <div className={styles.header}>
        <h2 className={styles.title}>{step.title}</h2>
        {step.icon}
      </div>
      <div className={styles.progressBarWrapper}>
        <div
          className={styles.progressBar}
          style={{ width: `${((currentStep + 1) / totalSteps) * 100}%` }}
        ></div>
      </div>
      <div className={styles.content}>{step.content}</div>
      {step.showControls && (
        <div className={styles.controls}>
          <Button
            color="default"
            variant="outlined"
            className={styles.button}
            onClick={prevStep}
            disabled={currentStep === 0}
          >
            Previous
          </Button>
          <Button
            color="default"
            variant="solid"
            className={styles.buttonNext}
            onClick={nextStep}
          >
            {currentStep === totalSteps - 1 ? "Finish" : "Next"}
          </Button>
        </div>
      )}
      {step.showSkip && (
        <Button
          color="default"
          onClick={skipTour}
          className={styles.buttonSkip}
        >
          Skip
        </Button>
      )}
      {arrow}
    </div>
  );
};

export default CustomTourCard;
