"use client";

import React from "react";
import {
  Box,
  Paper,
  Typography,
  Stack,
} from "@mui/material";
import Image from "next/image";

export interface SegmentData {
  item_id: string;
  speaker: string;
  timestamp_start: string;     
  timestamp_end: string;      
  add_to_history_time: string;
  transcript: string;
  previous_item_id?: string | null;
  response_id?: string;
}

interface ChatBubbleProps {
  id: string;
  name: string;
  role: string;
  time: string;
  date: string;
  message: string;
  avatarUrl?: string;
}

export default function ChatBubble({
  name,
  role,
  time,
  date,
  message,
}: ChatBubbleProps) {
  const avatarSrc = (role === "bot" ? '/bot.png' : '/avatar.svg');

  return (
    <Paper
      elevation={0}
      sx={{
        display: "flex",
        alignItems: "flex-start",
        gap: 2,
        p: 2,
        mb: 2,
      }}
    >
      <Image src={avatarSrc} alt="" style={{borderRadius: 50}} height={30} width={30}/>
      <Box sx={{ flexGrow: 1 }}>
        <Stack direction="row" justifyContent="space-between" alignItems="baseline">
          <Typography variant="subtitle2" fontWeight="bold" sx={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
            {name}
          </Typography>
          <Box>
            <Typography variant="caption" color="text.secondary" sx={{ mr: 1, fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
              {time}
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
              {date}
            </Typography>
          </Box>
        </Stack>
        <Typography variant="body2" sx={{ mt: 1, whiteSpace: "pre-line", fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
          {message}
        </Typography>
      </Box>
    </Paper>
  );
}

