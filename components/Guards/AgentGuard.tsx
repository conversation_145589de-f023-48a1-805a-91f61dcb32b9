"use client";

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useGeneralStore } from "@/providers/general-store-provider";

export default function AgentGuard({ children }: { children: React.ReactNode }) {
  const { agents } = useGeneralStore((s) => s);
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    if (agents.length === 0 && pathname !== "/starter") {
      router.push("/starter");
    }
  }, [agents, pathname, router]);

  return <>{children}</>;
}
