"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/stores/auth-store";
import { Spin } from "antd";

export default function AuthGuard({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, hydrated, user, logout } = useAuthStore((s) => s);
  const router = useRouter();

  useEffect(() => {
    if (!hydrated) return;

    if (!isAuthenticated || !user?.expiresIn || new Date(user.expiresIn).getTime() < Date.now()) {
      logout();
      router.push("/login");
    }
  }, [hydrated, isAuthenticated, user, logout, router]);

  if (!hydrated) return <Spin fullscreen />;
  if (!isAuthenticated) return null;

  return <>{children}</>;
}
