"use client";

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuthStore } from "@/stores/auth-store";

export default function SubscriptionGuard({ children }: { children: React.ReactNode }) {
  const { user } = useAuthStore((s) => s);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (user && !user.subscription && pathname !== "/pricing-list" && !user.isAdministrator) {
      router.push("/pricing-list");
    }
  }, [user, pathname, router]);

  if (!user || (!user.subscription && pathname !== "/pricing-list" && !user.isAdministrator)) return null;

  return <>{children}</>;
}
