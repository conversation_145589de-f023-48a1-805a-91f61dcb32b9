.createScenarioCard {
    min-height: 230px;
    border-radius: 24px !important;
    padding: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: #6941C6 !important;
    background-image: url('/Group.svg') !important;
    background-size: 40% !important;
    background-repeat: no-repeat !important;    
    background-position: right !important;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    width: 100%;
    max-width: 100%;
    /* Let the parent grid control the width, so it fits 3 per row */

    &:hover {
        background-color: #7F56D9;
    }
}

.createScenarioButton {
    background-color: rgba(255, 255, 255, 0.1) !important;
    width: 48px !important;
    height: 48px !important;
    margin-bottom: 16px !important;
    transition: all 0.2s ease-in-out;

    &:hover {
        background-color: rgba(255, 255, 255, 0.2) !important;
    }
}

.addIcon {
    color: white;
    font-size: 24px;
} 