import React, { useState } from "react";
import styles from "./ScenarioCard.module.scss";
import { Button, Menu, MenuItem, IconButton, Box } from "@mui/material";
import Image from "next/image";
import MoreHorizIcon from "@mui/icons-material/MoreHoriz";
import CircularProgress from "@mui/material/CircularProgress";
import EditScenarioTitleDialog from './EditScenarioTitleDialog';

export interface ScenarioCardProps {
  tag: string;
  title: string;
  description: string;
  metricsCount: number;
  onMetricsClick?: () => void;
  onPlayClick?: () => void;
  onActionClick?: () => void;
  checked?: boolean;
  onCheck?: (checked: boolean) => void;
  loading?: boolean;
  onPreview?: () => void;
  bundleId: string;
  onEditSuccess?: () => void;
}

const ScenarioCard: React.FC<ScenarioCardProps> = ({
  tag,
  title,
  description,
  metricsCount,
  onMetricsClick,
  onPlayClick,
  loading = false,
  onPreview,
  bundleId,
  onEditSuccess,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);

  return (
    <div className={styles.card} onClick={editDialogOpen ? undefined : onPreview}>
      <div className={styles.contentWrapper}>
        <div className={styles.header}>
          <Box className={styles.tag}>
            <Image
              src="/ai-voice-logo.svg"
              alt="voice logo"
              width={16}
              height={16}
              className={styles.tagIcon}
            />
            {tag}
          </Box>
          <Box>
            <IconButton
              className={styles.actionButton}
              aria-label="More actions"
              onClick={e => {
                e.stopPropagation();
                setAnchorEl(e.currentTarget);
              }}
              size="small"
            >
              <MoreHorizIcon style={{ fontSize: 20, color: '#18181b' }} />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={() => setAnchorEl(null)}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              transformOrigin={{ vertical: 'top', horizontal: 'right' }}
              onClick={e => e.stopPropagation()}
            >
              <MenuItem onClick={e => {
                e.stopPropagation();
                setAnchorEl(null);
                setEditDialogOpen(true);
              }}>Edit</MenuItem>
            </Menu>
          </Box>
        </div>

        <div>
          <div className={styles.title}>{title}</div>
          <div className={styles.description}>{description}</div>
        </div>
      </div>

      <div className={styles.footer}>
        <span className={styles.metrics} onClick={e => { e.stopPropagation(); onMetricsClick?.(); }}>
          <Image
            src="/metrics-vector.svg"
            alt="metrics icon"
            width={20}
            height={20}
            className={styles.metricsIcon}
          />
          <span className={styles.metricsText}>{metricsCount} Metrics</span>
        </span>
        <Button
          className={styles.playButton}
          onClick={e => { e.stopPropagation(); onPlayClick?.(); }}
          disabled={loading}
        >
          {loading ? (
            <CircularProgress size={18} thickness={5} />
          ) : (
            <Image
              src="/color-union.svg"
              alt="play"
              width={20}
              height={20}
            />
          )}
        </Button>
      </div>

      <EditScenarioTitleDialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        initialTitle={title}
        bundleId={bundleId}
        onSuccess={() => {
          if (onEditSuccess) onEditSuccess();
        }}
      />
    </div>
  );
};

export default ScenarioCard;