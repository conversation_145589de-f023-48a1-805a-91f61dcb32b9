.dialog {
  font-family: 'Plus Jakarta Sans', 'Inter', 'Segoe UI', Arial, sans-serif !important;
  border-radius: 24px !important;
  overflow: hidden;
  padding: 0 !important;
  box-shadow: 0 8px 32px 0 rgba(36, 0, 87, 0.10) !important;
}

.dialogTitle {
  padding: 32px 32px 0 32px !important;
  background: #fff;
}

.closeIcon {
  font-size: 28px;
  color: #bdbdbd;
  font-weight: 700;
  cursor: pointer;
  transition: color 0.2s;
  margin-right: -8px;
  &:hover {
    color: #9E77ED;
  }
}

.headerRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.tag {
  display: inline-flex;
  align-items: center;
  background: #f5f5f7;
  color: #222;
  font-size: 11px;
  font-weight: 500;
  border-radius: 16px;
  padding: 4px 14px 4px 10px;
  line-height: 1.5;
  gap: 6px;
}

.title {
  font-size: 24px;
  font-weight: 700;
  color: #222;
  margin-bottom: 8px;
  line-height: 1.2;
}

.description {
  font-size: 16px;
  color: #888;
  font-weight: 500;
  margin-bottom: 28px;
  line-height: 1.5;
}

.dialogContent {
  background: #fff;
  padding: 0 32px 0 32px !important;
}

.metricsBox {
  background: #fafafd;
  border-radius: 18px;
  padding: 20px 18px 16px 18px;
  margin-bottom: 28px;
  border: 1px solid #ececec;
}

.metricsLabel {
  font-size: 15px;
  font-weight: 700;
  color: #222;
  margin-bottom: 12px;
}

.metricsChips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 10px;
}

.metricChip {
  background: #f5f5f7;
  color: #222;
  font-size: 13px;
  font-weight: 500;
  border-radius: 12px;
  padding: 6px 16px;
  line-height: 1.3;
}

.runsRow {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  margin-bottom: 18px;
}

.runsLabel {
  font-size: 14px;
  font-weight: 500;
  color: #222;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
}

.runsInputBox {
  display: flex;
  align-items: center;
  width: 100%;
  height: 56px;
  background: transparent;
  box-shadow: none;
  gap: 5px;
}

.runsBtn {
  width: 56px;
  height: 56px;
  border: 1.5px solid #ececec !important;
  background: #fff; 
  font-size: 24px;
  font-weight: 700;
  color: #222;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s, border-color 0.2s;
  outline: none;
  box-shadow: none;
  border-radius: 16px;
}



.runsValue {
  flex: 1;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #222;
  background: #fff;
  border: 1.5px solid #ececec !important;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 2px;
  margin: 0;
}

.creditsRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 28px;
  background: #fafafd;
  border-radius: 16px;
  padding: 12px 18px;
  border: 1px solid #ececec;
}

.creditsLabel {
  font-size: 15px;
  color: #222;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.upgradeBtn {
  background: #f4ebff !important;
  color: #9E77ED !important;
  font-weight: 600;
  border-radius: 12px;
  border: none !important;
  font-size: 14px;
  padding: 4px 18px;
  box-shadow: none !important;
  transition: background 0.2s;
  &:hover {
    background: #ece6fa !important;
  }
}

.actionsRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 18px;
  margin: 0 32px 32px 32px !important;
  padding: 0 !important;
}

.editBtn {
  background: #fff !important;
  color: #9E77ED !important;
  font-weight: 700;
  border-radius: 12px;
  border: 1.5px solid #9E77ED !important;
  font-size: 16px;
  padding: 0 32px;
  height: 48px;
  box-shadow: none !important;
  transition: background 0.2s, color 0.2s;
  &:hover {
    background: #f4ebff !important;
    color: #7F56D9 !important;
  }
}

.runBtn {
  background: #9E77ED !important;
  color: #fff !important;
  font-weight: 700;
  border-radius: 12px;
  border: none !important;
  font-size: 16px;
  padding: 0 32px;
  height: 48px;
  box-shadow: none !important;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: background 0.2s;
  &:hover {
    background: #7F56D9 !important;
  }
}

@media (max-width: 700px) {
  .dialog {
    width: 98vw !important;
    min-width: 0 !important;
    padding: 0 !important;
  }
  .dialogTitle {
    padding: 24px 12px 0 12px !important;
  }
  .dialogContent {
    padding: 0 12px 0 12px !important;
  }
  .actionsRow {
    flex-direction: column;
    gap: 12px;
    margin: 0 12px 18px 12px !important;
  }
  .editBtn, .runBtn {
    width: 100%;
    padding: 0;
  }
} 