import React from "react";
import Card from "@mui/material/Card";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import AddIcon from "@mui/icons-material/Add";
import styles from "./CreateScenarioCard.module.scss";

interface CreateScenarioCardProps {
  onClick: () => void;
}

const CreateScenarioCard: React.FC<CreateScenarioCardProps> = ({ onClick }) => {
  return (
    <Card className={styles.createScenarioCard} onClick={onClick}>
      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <Box sx={{ textAlign: 'center' }}>
          <IconButton className={styles.createScenarioButton}>
            <AddIcon className={styles.addIcon} />
          </IconButton>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              fontSize: "20px",
              lineHeight: "30px",
              color: "white",
              fontFamily: "Plus Jakarta Sans",
              mb: 1
            }}
          >
            Create Scenario
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: "rgba(255, 255, 255, 0.8)",
              fontSize: "14px",
              lineHeight: "20px",
              fontFamily: "Plus Jakarta Sans",
              maxWidth: "320px"
            }}
          >
            Create a new scenario to evaluate your agent
          </Typography>
        </Box>
      </Box>
    </Card>
  );
};

export default CreateScenarioCard; 