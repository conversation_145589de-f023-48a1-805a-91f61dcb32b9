import React, { useState } from "react";
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import CloseIcon from '@mui/icons-material/Close';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import Image from "next/image";
import styles from "./ScenarioDetailsDialog.module.scss";

export interface ScenarioDetailsDialogProps {
  open: boolean;
  onClose: () => void;
  scenario: {
    tag: string;
    title: string;
    description: string;
    metrics: string[];
    creditsRequired: number;
  };
  onRun: (runs: number) => void;
  onEdit: () => void;
  maxRuns?: number;
  minRuns?: number;
}

const ScenarioDetailsDialog: React.FC<ScenarioDetailsDialogProps> = ({
  open,
  onClose,
  scenario,
  onRun,
  onEdit,
  maxRuns = 99,
  minRuns = 1,
}) => {
  const [runs, setRuns] = useState(4);

  const handleDecrement = () => setRuns((r) => Math.max(minRuns, r - 1));
  const handleIncrement = () => setRuns((r) => Math.min(maxRuns, r + 1));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      classes={{ paper: styles.dialogPaper }}
      PaperProps={{ className: styles.dialog }}
    >
      <DialogTitle className={styles.dialogTitle}>
        <div className={styles.headerRow}>
          <span className={styles.tag}>
            <Image src="/ai-voice-logo.svg" alt="voice logo" width={16} height={16} />
            {scenario.tag}
          </span>
          <IconButton className={styles.closeIcon} onClick={onClose} size="large">
            <CloseIcon fontSize="inherit" />
          </IconButton>
        </div>
        <div className={styles.title}>{scenario.title}</div>
        <div className={styles.description}>{scenario.description}</div>
      </DialogTitle>
      <DialogContent className={styles.dialogContent}>
        <div className={styles.metricsBox}>
          <div className={styles.metricsLabel}>Testing Metrics</div>
          <div className={styles.metricsChips}>
            {scenario.metrics.map((m, i) => (
              <span className={styles.metricChip} key={i}>{m}</span>
            ))}
          </div>
        </div>
        <div className={styles.runsRow}>
          <div className={styles.runsLabel}>
            Number of Runs
            <Tooltip title="How many times to run this scenario.">
              <InfoOutlinedIcon fontSize="small" style={{ marginLeft: 4, verticalAlign: 'middle' }} />
            </Tooltip>
          </div>
          <div className={styles.runsInputBox}>
            <button className={styles.runsBtn} onClick={handleDecrement} disabled={runs <= minRuns} type="button">
              <Image src="/minus-union.svg" alt="-" width={15} height={3} />
            </button>
            <span className={styles.runsValue}>{runs.toString().padStart(2, '0')}</span>
            <button className={styles.runsBtn} onClick={handleIncrement} disabled={runs >= maxRuns} type="button">
              <Image src="/plus-union.svg" alt="+" width={15} height={15} />
            </button>
          </div>
        </div>
        <div className={styles.creditsRow}>
          <span className={styles.creditsLabel}>
            <Image src="/credits.svg" alt="credits" width={20} height={20} /> Credits Required: <b>{scenario.creditsRequired.toFixed(2)}</b>
          </span>
          <Button className={styles.upgradeBtn}>Upgrade</Button>
        </div>
      </DialogContent>
      <DialogActions className={styles.actionsRow}>
        <Button className={styles.editBtn} onClick={onEdit} size="large">Edit Scenario</Button>
        <Button className={styles.runBtn} onClick={() => onRun(runs)} size="large" startIcon={<Image src="/color-union.svg" alt="run" width={18} height={18} />}>
          Run Scenario
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ScenarioDetailsDialog; 