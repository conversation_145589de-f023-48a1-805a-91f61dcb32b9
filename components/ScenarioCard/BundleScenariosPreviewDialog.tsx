import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';
import CloseIcon from '@mui/icons-material/Close';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { IScenario } from '@/types/scenario';
import styles from './BundleScenariosPreviewDialog.module.scss';

interface BundleScenariosPreviewDialogProps {
  open: boolean;
  scenarioBundle: IScenario;
  onClose: () => void;
}

const BundleScenariosPreviewDialog: React.FC<BundleScenariosPreviewDialogProps> = ({ open, scenarioBundle, onClose }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '24px',
          backgroundColor: '#FFFFFF',
          width: 565,
        },
        className: styles.dialog,
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          fontFamily: 'Plus Jakarta Sans',
          fontWeight: 800,
          fontSize: 24,
          pt: 3,
          pb: 2,
        }}
        className={styles.dialogTitle}
      >
        <Typography variant="h5" sx={{ fontWeight: 800, fontFamily: 'Plus Jakarta Sans', fontSize: 24 }}>
          Scenario Bundle Preview
        </Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ pt: 3, pb: 3, mt: 2 }} className={styles.dialogContent}>
        {scenarioBundle.scenarios && scenarioBundle.scenarios.length > 0 ? (
          scenarioBundle.scenarios.map((sc, idx) => (
            <Accordion key={sc.id} defaultExpanded={idx === 0} sx={{ mb: 2, borderRadius: '16px', boxShadow: 'none', border: '1px solid #ECECEC' }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />} sx={{ fontWeight: 700, fontFamily: 'Plus Jakarta Sans', fontSize: 18 }}>
                <Typography sx={{ fontWeight: 700, fontFamily: 'Plus Jakarta Sans', fontSize: 18 }}>{sc.title}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography sx={{ whiteSpace: 'pre-line', color: '#555', fontFamily: 'Plus Jakarta Sans', fontSize: 15 }}>{sc.prompt}</Typography>
              </AccordionDetails>
            </Accordion>
          ))
        ) : (
          <Typography color="text.secondary" align="center" sx={{ my: 4, fontFamily: 'Plus Jakarta Sans' }}>
            No nested scenarios found in this bundle.
          </Typography>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default BundleScenariosPreviewDialog; 