.card {
  font-family: 'Plus Jakarta Sans', 'Inter', 'Segoe UI', Arial, sans-serif;
  background: #fff;
  border-radius: 24px; // rounded-3xl
  padding: 20px; // p-5
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 288px; // h-72 in Tailwind (18rem = 288px)
  position: relative;
  outline: 1px solid #e5e7eb; // outline outline-1 outline-offset-[-1px] outline-gray-200
  outline-offset: -1px;
  transition: box-shadow 0.2s, outline-color 0.2s;
  cursor: pointer;

  &:hover {
    box-shadow: 0 4px 16px rgba(127, 86, 217, 0.15);
    outline-color: #9E77ED; // Change to violet-400 on hover

    .actionButton {
      opacity: 1; // Show on hover
    }
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  margin-bottom: 8px;
  width: 100%; // self-stretch
}

.tag {
  align-items: center;
  width: fit-content;
  background: #f5f5f5; // bg-neutral-100
  display: flex;
  color: #18181b; // text-zinc-900
  font-size: 12px; // text-xs
  font-weight: 500; // font-medium
  border-radius: 12px; // rounded-xl
  padding: 4px 8px; // px-2 py-1
  line-height: 1; // leading-none
  gap: 4px;
  justify-content: center; // justify-center items-center
}

.tagIcon {
  width: 16px; // w-4 h-4
  height: 16px;
  color: #737373; // neutral-500 equivalent
  position: relative;
}

.title {
  font-size: 16px; // text-base
  font-weight: 700; // font-bold
  color: #18181b; // text-zinc-900
  margin-bottom: 8px;
  line-height: 1.5; // leading-normal
  width: 100%; // self-stretch
}

.description {
  font-size: 12px; // text-xs
  font-weight: 500; // font-medium
  color: #737373; // text-neutral-500
  line-height: 1; // leading-none
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  width: 100%; // self-stretch
}

.contentWrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%; // self-stretch
  align-items: flex-start; // items-start
}

.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%; // self-stretch
}

.metrics {
  display: flex;
  align-items: center;
  color: #7F56D9; // text-violet-500
  font-weight: 600; // font-semibold
  font-size: 14px; // text-sm
  cursor: pointer;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 8px; // rounded-lg
  justify-content: center; // justify-center items-center
}

.metricsIcon {
  width: 20px; // w-5 h-5
  height: 20px;
  color: #7F56D9; // violet-500 equivalent
  position: relative;
  overflow: hidden;
}

.metricsText {
  color: #7F56D9; // text-violet-500
  font-size: 14px; // text-sm
  font-weight: 600; // font-semibold
  line-height: 1; // leading-none
}

.playButton {
  background: rgba(0, 0, 0, 0.05) !important; // bg-black/5
  box-shadow: none !important;
  width: 48px !important; // w-12 h-12
  height: 48px !important;
  display: flex;
  min-width: 0px !important;
  border-radius: 260px !important; // rounded-[260px]
  align-items: center;
  justify-content: center;
  border: none;
  padding: 0;
  transition: background 0.2s;
  gap: 12px;

  &>span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  img {
    padding: 0px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.08) !important;
  }
}

.actionButton {
  position: relative;
  background: transparent;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
  width: 24px;
  z-index: 2;
  transition: opacity 0.2s;
  opacity: 0; // opacity-0 (hidden by default, shown on hover)
  overflow: hidden; // overflow-hidden

  &:hover,
  &:focus {
    background: rgba(0, 0, 0, 0.05);
    outline: none;
  }
}


@media (max-width: 900px) {
  .card {
    height: 260px;
    padding: 16px;
  }
}

@media (max-width: 600px) {
  .card {
    height: 240px;
    padding: 12px;
  }

  .title {
    font-size: 14px;
  }

  .description {
    font-size: 12px;
  }

  .metrics {
    font-size: 12px;
  }

  .metricsText {
    font-size: 12px;
  }

  .playButton {
    width: 40px !important;
    height: 40px !important;
  }
}