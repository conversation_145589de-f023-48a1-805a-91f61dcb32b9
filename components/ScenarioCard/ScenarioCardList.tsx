import React from "react";
import ScenarioCard, { ScenarioCardProps } from "./ScenarioCard";
import styles from "./ScenarioCardList.module.scss";

export interface ScenarioCardListProps {
  scenarios: ScenarioCardProps[];
}

const ScenarioCardList: React.FC<ScenarioCardListProps> = ({ scenarios }) => {
  return (
    <div className={styles.grid}>
      {scenarios.map((scenario, idx) => (
        <ScenarioCard key={idx} {...scenario} />
      ))}
    </div>
  );
};

export default ScenarioCardList; 