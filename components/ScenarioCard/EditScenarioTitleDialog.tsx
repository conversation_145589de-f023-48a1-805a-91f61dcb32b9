import React, { useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import EditIcon from '@mui/icons-material/Edit';
import { Box, Typography, CircularProgress } from '@mui/material';
import axiosInstance from '@/utils/axiosInstance';

interface EditScenarioTitleDialogProps {
  open: boolean;
  onClose: () => void;
  initialTitle: string;
  bundleId: string;
  onSuccess?: (newTitle: string) => void;
}

const EditScenarioTitleDialog: React.FC<EditScenarioTitleDialogProps> = ({
  open,
  onClose,
  initialTitle,
  bundleId,
  onSuccess,
}) => {
  const [title, setTitle] = useState(initialTitle);
  const [loading, setLoading] = useState(false);
  const [touched, setTouched] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const TITLE_MAX_LENGTH = 50;

  const handleSave = async () => {
    if (!title.trim()) {
      setError('Bundle Title is required');
      setTouched(true);
      return;
    }
    if (title.length > TITLE_MAX_LENGTH) {
      setError(`Title must be ${TITLE_MAX_LENGTH} characters or less`);
      setTouched(true);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const response = await axiosInstance(`${process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS}/bundles/${bundleId}`, {
        method: 'PUT',
        data: { title },
      });
      if (response.status !== 200 && response.status !== 204) {
        setError('Failed to update title');
        return;
      }
      if (onSuccess) onSuccess(title);
      onClose();
    } catch {
      setError('Unexpected error');
    } finally {
      setLoading(false);
    }
  };

  const isValid = !!title.trim() && title.length <= TITLE_MAX_LENGTH;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: '24px', width: 520, backgroundColor: '#FFFFFF' },
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          fontFamily: 'Plus Jakarta Sans',
          fontWeight: 800,
          fontSize: 24,
          pt: 3,
          pb: 2,
        }}
      >
        <Box
          sx={{
            width: 44,
            height: 44,
            borderRadius: '14.5px',
            background: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 2px 8px 0 rgba(16, 24, 40, 0.10)',
          }}
        >
          <EditIcon sx={{ color: '#7F56D9', fontSize: 24 }} />
        </Box>
        <Typography
          sx={{ fontWeight: 700, fontFamily: 'Plus Jakarta Sans', fontSize: 20, color: '#1C1C1C' }}
        >
          Edit Bundle Title
        </Typography>
        <Box flexGrow={1} />
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ pt: 3, pb: 0 }}>
        <TextField
          autoFocus
          fullWidth
          value={title}
          onChange={e => { setTitle(e.target.value); setTouched(true); setError(null); }}
          onBlur={() => setTouched(true)}
          placeholder="Enter scenario title"
          error={touched && (!title.trim() || title.length > TITLE_MAX_LENGTH)}
          helperText={
            touched && (!title.trim() ? 'Title is required' :
              title.length > TITLE_MAX_LENGTH ? `Title must be ${TITLE_MAX_LENGTH} characters or less` : `${title.length}/${TITLE_MAX_LENGTH}`)
          }
          inputProps={{ maxLength: TITLE_MAX_LENGTH }}
          sx={{
            fontFamily: 'Plus Jakarta Sans',
            borderRadius: '16px',
            mt: 1,
            mb: 2,
            '& .MuiOutlinedInput-root': {
              borderRadius: '16px',
              fontSize: 18,
              fontWeight: 500,
              background: '#F6F6F6',
              '& input::placeholder': {
                color: '#BDBDBD',
                opacity: 1,
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: '#7F56D9',
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#7F56D9',
              },
            },
          }}
        />
        {error && (
          <Typography color="error" sx={{ fontFamily: 'Plus Jakarta Sans', fontSize: 14, mt: 1 }}>{error}</Typography>
        )}
      </DialogContent>
      <DialogActions sx={{ px: 3, pb: 3, pt: 2, borderTop: 'none', display: 'flex', flexDirection: 'row', gap: 2 }}>
        <Button
          onClick={onClose}
          variant="outlined"
          fullWidth
          sx={{
            background: '#F6F6F6',
            borderRadius: '16px',
            fontWeight: 500,
            fontSize: 16,
            color: '#595959',
            height: '56px',
            boxShadow: 'none',
            textTransform: 'none',
            fontFamily: 'Plus Jakarta Sans',
            borderColor: 'transparent',
            flex: 1,
            '&:hover': { background: '#F0F0F2' },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          fullWidth
          sx={{
            backgroundColor: '#7F56D9',
            borderRadius: '16px',
            fontWeight: 600,
            fontSize: 16,
            color: '#fff',
            height: '56px',
            boxShadow: 'none',
            fontFamily: 'Plus Jakarta Sans',
            textTransform: 'none',
            flex: 1,
            '&:hover': { backgroundColor: '#6941C6' },
          }}
          disabled={!isValid || loading}
        >
          {loading ? <CircularProgress size={22} sx={{ color: '#fff' }} /> : 'Save'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditScenarioTitleDialog; 