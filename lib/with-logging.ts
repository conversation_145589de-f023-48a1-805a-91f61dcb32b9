import { NextRequest } from 'next/server'

export async function withLogging(
    req: NextRequest,
    handler: () => Promise<Response>
): Promise<Response> {
    const start = Date.now()
    try {
        const res = await handler()
        const duration = Date.now() - start
        console.log(`[${req.method}] ${req.url} - ${res.status} (${duration}ms)`)
        return res
    } catch (err: any) {
        const duration = Date.now() - start
        console.error(`[${req.method}] ${req.url} - ERROR (${duration}ms):`, err)
        return new Response(JSON.stringify({ error: 'Internal Server Error' }), { status: 500 })
    }
}