import { NextRequest } from 'next/server'

interface User {
    id: string
    email: string
}

async function validateToken(token?: string): Promise<User | null> {
    // Replace with real token validation logic (e.g., JWT, database lookup)
    if (token === 'valid-token') {
        return { id: 'user123', email: '<EMAIL>' }
    }
    return null
}

export async function withAuth(
    req: NextRequest,
    handler: (user: User) => Promise<Response>
): Promise<Response> {
    const token = req.headers.get('Authorization')?.replace('Bearer ', '')
    const user = await validateToken(token)

    if (!user) {
        return new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401 })
    }

    return handler(user)
}