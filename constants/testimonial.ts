export const TESTIMONIAL_DATA = [
    {
        id: "1",
        name: "<PERSON>",
        role: "Conversational AI Lead",
        company: "VoxTech",
        photoUrl: "/voxtech.avif",
        backgroundColor: "#ff8668",
        text: "Before TestAI, testing our voice assistants was tedious. Now, with automated test coverage and AI-driven simulations, we proactively catch failures before deployment. Our response accuracy jumped by 40%, and we’ve reduced testing time by 60%. A game-changer for our development cycle.",
    },
    {
        id: "2",
        name: "<PERSON>",
        role: "Head of AI",
        company: "CallFlow",
        photoUrl: "/callflow-logo.png",
        backgroundColor: "#dcc0dd",
        text: "TestAI transformed how we validate voice AI performance. Real-world simulations uncovered issues we'd never catch manually. Our bot’s user satisfaction score rose by 25%, and support escalations dropped significantly.",
    },
    {
        id: "3",
        name: "<PERSON><PERSON>.",
        role: "CEO",
        company: "Pipeshift",
        photoUrl: "/pipeshift.webp",
        backgroundColor: "#B8D891",
        text: "TestAI revolutionized our voice bot testing process. We used to rely on manual QA, which was slow and error-prone. Now, with automated scenario generation and real-time monitoring, we identify and fix edge cases before they impact customers. Our bot’s accuracy improved by 35% in just two months!",
    },
];