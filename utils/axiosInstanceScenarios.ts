import { useAuthStore } from '@/stores/auth-store';
import axios from 'axios';
import { redirect } from 'next/navigation';

const axiosInstanceScenarios = axios.create({
    baseURL: process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS,
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  axiosInstanceScenarios.interceptors.request.use(
    (config) => {
      const token = useAuthStore.getState().user?.token;
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );
  
  console.log('axiosInstanceScenarios', axiosInstanceScenarios);
  axiosInstanceScenarios.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response && error.response.status === 401) {
        useAuthStore.getState().logout();
  
        if (typeof window !== 'undefined') {
          redirect('/login');
        }
      }
      return Promise.reject(error);
    }
  );
  
  export default axiosInstanceScenarios;