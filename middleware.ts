import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Example: check for a token in cookies
  const token = request.cookies.get('token')?.value;

  // If no token, redirect to login
  if (!token) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // Optionally, add token expiry check here if you encode expiry in the token or cookie
  // For example, if using JWT, you could decode and check expiry

  return NextResponse.next();
}

// Apply middleware only to protected routes
export const config = {
  matcher: ['/app/(dashboard)/:path*', '/app/(starter)/:path*'],
}; 