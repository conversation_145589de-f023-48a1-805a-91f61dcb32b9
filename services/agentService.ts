import axiosInstance from '@/utils/axiosInstance';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS || '';

export const generateAgentDescription = async (description?: string, templateId?: number | null) => {
  try {
    // Build the payload dynamically
    const payload: Record<string, any> = {};
    if (templateId !== undefined && templateId !== null) {
      payload.agent_id = templateId;
    }
    if (description) {
      payload.description = description;
    }

    const response = await axiosInstance.post(`${BASE_URL}/agents/description/generate`, payload);
    return response.data;
  } catch (error) {
    console.error('Error generating agent description:', error);
    throw error;
  }
}; 