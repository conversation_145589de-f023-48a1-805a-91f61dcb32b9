"use client";

import { useNotification } from "@/context/NotificationContext/NotificationContext";
import { useGeneralStore } from "@/providers/general-store-provider";
import axiosInstanceScenarios from "@/utils/axiosInstanceScenarios";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";

export interface AgentData {
  id: number | string;
  name: string;
  contact_number: string;
  description: string;
  language: string;
  knowledge_base_ids: number[];
}

export async function fetchAgentsData(): Promise<AgentData[]> {
  const response = await axiosInstanceScenarios.get("/agents");
  return response.data;
}

export default function useAgentsData() {
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const notify = useNotification();
  const { setAgents, setCurrentAgentId } = useGeneralStore((state) => state);

  const getAgentsData = async (): Promise<AgentData[] | null> => {
    try {
      const agentsData = await fetchAgentsData();
      if(agentsData && agentsData.length > 0) {
        setAgents(
          agentsData.map((agent: AgentData) => ({
            id: String(agent.id),
            name: agent.name,
            contact_number: agent.contact_number,
            description: agent.description,
            language: agent.language,
            knowledge_base_ids: agent.knowledge_base_ids,
          })),
        );
        setCurrentAgentId(String(agentsData[agentsData.length - 1].id));
      }

      return agentsData;
    } catch (error: any) {
      notify.error({
        message: "Failed to get agents",
        description: error.response.data.detail,
      });
      generalAnalyticsEvents.trackApiRequestFailed(
        "GET /agents",
        String(error.response?.status),
        `Failed to get agents: ${error.message}`,
      );
      return null;
    }
  };

  return { getAgentsData };
}
