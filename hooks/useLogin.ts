"use client";

import { useState } from "react";
import { add } from "date-fns";
import * as jose from "jose";
import { usePathname, useRouter } from "next/navigation";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import { useAuthStore } from "@/stores/auth-store";
import { IJwtPayload } from "@/types/user";
import useUserData from "@/hooks/useUserData";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";
import useAgentsData from "@/hooks/useAgentsData";
import { generalStore } from "@/stores/general-store";

interface LoginValues {
  email: string;
  password: string;
}

export default function useLogin() {
  const { login, setCurrentUserData } = useAuthStore((state) => state);
  const notify = useNotification();
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const analytics = useRudderStackAnalytics();
  const pathname = usePathname();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const { getUserData } = useUserData();
  const { getAgentsData } = useAgentsData();

  const loginApiCall = async (credentials: LoginValues) => {
    const response = await fetch("/api/login", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(credentials),
    });
    if (!response.ok) {
      if (response.status === 401) {
        notify.error({
          message: "Authentication Failed",
          description: "Incorrect email or password",
          duration: null,
        });
      } else {
        notify.error({
          message: "Authentication Failed",
          description: "Something went wrong. Please try again later.",
        });
      }
      generalAnalyticsEvents.trackApiRequestFailed(
        "POST /login",
        String(response.status),
        "Failed to login",
      );
      return null;
    }
    return await response.json();
  };

  const processToken = (loginResponse: any) => {
    const [hours, minutes, seconds] = loginResponse.access_token_expires
      .split(":")
      .map(Number);
    const expirationTime = add(new Date(), { hours, minutes, seconds });
    const tokenData: IJwtPayload = jose.decodeJwt(loginResponse.access_token);
    return { tokenData, expirationTime };
  };

  const loginUser = async (values: LoginValues) => {
    // Reset general store on login
    generalStore.getState().resetGeneralStore();
    setLoading(true);
    try {
      analytics?.track("Log In", {
        email: values.email,
        domain: values.email.split("@")[1],
        app: "test.ai",
        page_name: pathname,
      });

      const loginResponse = await loginApiCall(values);
      if (!loginResponse) return;

      const { tokenData, expirationTime } = processToken(loginResponse);
      analytics?.track("Logged In", {
        email: values.email,
        domain: values.email.split("@")[1],
        userId: tokenData?.id,
        app: "test.ai",
        page_name: pathname,
      });

      login({
        id: tokenData.id,
        email: values.email,
        token: loginResponse.access_token,
        tokenType: loginResponse.token_type,
        expiresIn: expirationTime,
        isAdministrator: tokenData.admin,
      });

      let userData;
      let agentsData;
      if (!tokenData.admin) {
        [agentsData, userData] = await Promise.all([
          getAgentsData(),
          getUserData(),
        ]);
        if (!agentsData || !userData) return;
      } else {
        userData = await getUserData();
        if (!userData) return;
      }

      setCurrentUserData({
        id: String(userData.id),
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        active: userData.active,
        call_limit: userData.call_limit,
        call_fact: userData.call_fact,
        company_name: userData.company_name,
        isAdministrator: tokenData.admin,
        token: loginResponse.access_token,
        tokenType: loginResponse.token_type,
        expiresIn: expirationTime,
        subscription: userData.subscription,
      });

      // Redirection logic after store is up to date

      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log("tokenData", tokenData);
      debugger
      if (tokenData.admin) {
        router.replace("/admin/users");
        return;
      } else if (userData.subscription && agentsData && agentsData.length > 0) {
        router.replace("/home");
        return;
      } else if (userData.subscription && (!agentsData || agentsData.length === 0)) {
        router.replace("/starter");
        return;
      } else if (!userData.subscription) {
        router.replace("/pricing-list");
        return;
      }
    } catch (e) {
      console.error("Login error:", e);
      notify.error({
        message: "Unexpected Error",
        description: "Something went wrong. Please try again later.",
      });
    } finally {
      setLoading(false);
    }
  };

  return { loginUser, loading };
}
