import { useState } from 'react';
import { generateAgentDescription } from '@/services/agentService';
import { useNotification } from '@/context/NotificationContext/NotificationContext';

export const useAgentDescription = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const notify = useNotification();

  const generateDescription = async (currentDescription: string, templateId: number | null) => {
    try {
      setIsGenerating(true);
      const response = await generateAgentDescription(
        currentDescription,
        templateId
      );
      return response;
    } catch (error) {
      console.error('Error generating description:', error);
      notify.error({
        message: 'Failed to generate description',
        description: 'Please try again later',
      });
      throw error;
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    isGenerating,
    generateDescription
  };
}; 