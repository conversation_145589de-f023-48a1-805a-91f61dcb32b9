import { useState } from "react";
import { TemplateCardData } from "@/components/TemplateCard/TemplateCard";
import { useGeneralStore } from "@/providers/general-store-provider";
import { useNotification } from "@/context/NotificationContext/NotificationContext";

export default function useAgentLogic() {
    const [selectedTemplate, setSelectedTemplate] = useState<TemplateCardData | null>(null);
    const [updateAgent, setUpdateAgent] = useState<string | null>(null);
    const { agents, setAgents, currentAgentId, setCurrentAgentId } = useGeneralStore((state) => state);
    const notify = useNotification();

    const handleTemplateSelect = (data: TemplateCardData) => {
        setSelectedTemplate(data);
    };

    const handleOnCancel = () => {
        setSelectedTemplate(null);
        setUpdateAgent(null);
    };

    const handleSelectChange = () => {
        setUpdateAgent("new");
    };

    const handleCreateAgent = () => {
        setUpdateAgent("new");
    };

    const handleEditAgent = (agent: TemplateCardData) => {
        setUpdateAgent(agent.id);
    };

    return {
        selectedTemplate,
        updateAgent,
        agents,
        currentAgentId,
        setAgents,
        setCurrentAgentId,
        handleTemplateSelect,
        handleOnCancel,
        handleSelectChange,
        handleCreateAgent,
        handleEditAgent,
        notify,
    };
}
