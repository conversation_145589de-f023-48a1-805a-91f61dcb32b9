"use client";

import { useCallback } from "react";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import { IUser } from "@/types/user";
import axiosInstance from "@/utils/axiosInstance";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";

export async function fetchUserData(): Promise<IUser | null> {
  const response = await axiosInstance.get("/api/user");
  return response.data;
}

export default function useUserData() {
  const notify = useNotification();
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();

  const getUserData = useCallback(async (): Promise<IUser | null> => {
    try {
      return await fetchUserData();
    } catch (error: any) {
      generalAnalyticsEvents.trackApiRequestFailed(
        "GET /user",
        String(error.response?.status),
        `Failed to get user: ${error.message}`,
      );
      notify.error({
        message: "Failed to get user",
        description: error.message,
      });
      return null;
    }
  }, [notify]);

  return { getUserData };
}
