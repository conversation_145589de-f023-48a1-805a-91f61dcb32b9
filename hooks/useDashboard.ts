import { useState, useEffect } from 'react';
import { useAuthStore } from '@/stores/auth-store';

export interface DashboardStats {
  no_of_calls: number
  avg_duration: string
  completed_calls_average: string
  failed_calls_average: string
  chart_data: ChartDaum[]
}
export interface ChartDaum {
  month: string
  year: number
  runs: number
}

interface UseDashboardReturn {
  stats: DashboardStats | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

const useDashboard = (): UseDashboardReturn => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchStats = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const token = useAuthStore.getState().user?.token;
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS}/stats`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch dashboard stats'));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return {
    stats,
    isLoading,
    error,
    refetch: fetchStats
  };
};

export default useDashboard;
