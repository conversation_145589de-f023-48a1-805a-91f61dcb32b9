import { useEffect, useRef } from "react";
import { useAuthStore } from "@/stores/auth-store";
import { add } from "date-fns";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";
import { usePathname } from "next/navigation";

const useTokenRefresh = () => {
  const { user, setCurrentUserData } = useAuthStore();
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const lastRefreshRef = useRef(Date.now());
  const pathname = usePathname();

  useEffect(() => {
    if (!user || !user.expiresIn) return;

    const refreshToken = async () => {
      try {
        lastRefreshRef.current = Date.now();

        const response = await fetch("/api/token/refresh", {
          method: "GET",
          headers: {
            Authorization: `Bearer ${user.token}`,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to refresh token");
        }
        const refreshResponse = await response.json();

        const newToken = refreshResponse.access_token;

        const [hours, minutes, seconds] = refreshResponse.access_token_expires
          .split(":")
          .map(Number);

        const expirationTime = add(new Date(), { hours, minutes, seconds });

        setCurrentUserData({
          ...user,
          token: newToken,
          expiresIn: expirationTime,
          isAdministrator: refreshResponse.admin,
        });
      } catch (error) {
        generalAnalyticsEvents.trackApiRequestFailed(
          "GET /token/refresh",
          "500",
          `Unexpected Error: ${error}`,
        );
        generalAnalyticsEvents.trackSessionTimeout(
          Date.now() - lastRefreshRef.current,
          pathname,
        );
        console.error("Failed to refresh token:", error);
      }
    };

    const timeUntilExpiration =
      new Date(user.expiresIn).getTime() - new Date().getTime();
    const timeoutId = setTimeout(refreshToken, timeUntilExpiration - 30 * 1000);

    return () => clearTimeout(timeoutId);
  }, [user, setCurrentUserData]);
};

export default useTokenRefresh;
