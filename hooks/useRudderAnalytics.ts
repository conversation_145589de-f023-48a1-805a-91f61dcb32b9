import { useEffect, useState } from "react";
import type { RudderAnalytics } from "@rudderstack/analytics-js";

let analyticsInstance: RudderAnalytics | undefined;

const useRudderStackAnalytics = (): RudderAnalytics | undefined => {
  const [analytics, setAnalytics] = useState<RudderAnalytics | undefined>(
    analyticsInstance,
  );

  useEffect(() => {
    if (!analyticsInstance) {
      const initialize = async () => {
        const { RudderAnalytics } = await import("@rudderstack/analytics-js");
        analyticsInstance = new RudderAnalytics();

        analyticsInstance.load(
          process.env.NEXT_PUBLIC_RUDDERSTACK_WRITE_KEY as string,
          process.env.NEXT_PUBLIC_RUDDERSTACK_DATA_PLANE_URL as string,
        );

        analyticsInstance.ready(() => {
          console.log("useRudderStackAnalytics We are all set!!!");
        });

        setAnalytics(analyticsInstance);
      };

      initialize().catch((e) => console.log(e));
    } else {
      setAnalytics(analyticsInstance);
    }
  }, []);

  return analytics;
};

export default useRudderStackAnalytics;
